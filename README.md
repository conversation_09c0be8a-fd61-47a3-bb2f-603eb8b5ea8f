# 🤖 AI数据分析平台 V2.0

基于通义千问的智能数据分析平台，采用模块化架构设计。

## ✨ 新版本亮点

- 🏗️ **模块化架构** - 遵循Streamlit最佳实践，避免类重定义问题
- 🔧 **统一配置管理** - 集中管理所有配置项，支持环境变量
- 🎯 **关注点分离** - API客户端、处理器、集成层各司其职
- 🚀 **高可扩展性** - 新功能可以独立开发和测试
- 📊 **完善的日志** - 详细的操作日志和错误追踪
- 🔒 **安全增强** - 代码执行安全检查和API密钥验证

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository_url>
cd Project_test

# 创建虚拟环境（推荐）
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r config/requirements.txt
```

### 2. 配置设置
```bash
# 复制配置模板
cp config/.env.example config/.env

# 编辑配置文件，填入您的API密钥
# TONGYI_API_KEY=your_api_key_here
```

### 3. 验证配置
```bash
# 运行配置验证脚本
python scripts/validate_config.py
```

### 4. 启动应用
```bash
# 方式1: 直接启动
streamlit run streamlit_app.py

# 方式2: 使用启动脚本（推荐）
# Windows
scripts/start_app_v2.bat

# Linux/Mac
bash scripts/start_app.sh
```

## 📁 项目结构

```
├── app/                    # 主应用目录
│   ├── main.py            # 主应用文件
│   ├── config.py          # 应用配置
│   └── pages/             # 多页面应用
├── core/                  # 核心模块
│   ├── llm/              # LLM集成
│   ├── processors/       # 数据处理器
│   ├── utils/            # 工具模块
│   └── integrations/     # 外部集成
├── config/               # 配置文件
├── docs/                 # 文档
├── tests/                # 测试文件
├── scripts/              # 脚本文件
├── legacy/               # 旧版本文件
├── data/                 # 数据目录
├── logs/                 # 日志目录
└── cache/                # 缓存目录
```

## 🔧 功能特性

### 核心功能
- 🤖 **智能数据分析** - 基于通义千问的自然语言数据分析
- 📊 **自动图表生成** - 智能选择合适的图表类型
- 🔧 **代码自动修复** - 自动修复生成代码中的常见问题
- 📝 **Streamlit原生支持** - 完美集成Streamlit组件

### 数据处理
- 📁 **多格式支持** - CSV、Excel、JSON、TXT等格式
- 🧹 **智能数据清理** - 自动处理缺失值和异常数据
- 📈 **元数据增强** - 利用数据元信息提升分析质量
- 💾 **历史记录** - 完整的分析历史和结果保存

### 技术特性
- 🏗️ **模块化架构** - 清晰的代码结构，易于维护
- ⚙️ **统一配置** - 集中的配置管理系统
- 📋 **详细日志** - 完善的日志记录和错误追踪
- 🔒 **安全保障** - 代码执行安全检查
- 🚀 **高性能** - 优化的缓存和并发处理

## 📖 详细文档

### 架构文档
- [📋 架构设计方案](docs/architecture_plan.md) - 详细的模块化架构设计
- [🔗 依赖关系优化](docs/dependency_optimization_report.md) - 依赖关系分析和优化建议
- [🚀 迁移指南](docs/migration_guide.md) - 从旧版本迁移的完整指南

### 操作文档
- [⚙️ 配置说明](config/config_report.md) - 详细的配置项说明
- [📊 清理报告](docs/cleanup_summary.md) - 项目清理和重构记录
- [🧪 测试指南](tests/README.md) - 测试用例和测试方法

### API文档
- [🤖 LLM集成API](core/llm/README.md) - LLM模块使用说明
- [🔧 处理器API](core/processors/README.md) - 数据处理器使用说明
- [🔌 集成API](core/integrations/README.md) - 外部集成接口说明

## 🔄 版本升级

### 从V1.x升级到V2.0
如果您正在从旧版本升级，请参考 [迁移指南](docs/migration_guide.md)。

主要变化：
- 🏗️ 全新的模块化架构
- ⚙️ 统一的配置管理系统
- 🔧 改进的错误处理机制
- 📊 增强的日志记录功能

### 配置迁移
```bash
# 运行迁移脚本
python scripts/migrate_config.py

# 验证新配置
python scripts/validate_config.py
```

## 🛠️ 开发指南

### 开发环境设置
```bash
# 安装开发依赖
pip install -r config/requirements-dev.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black core/ app/ scripts/

# 类型检查
mypy core/ app/
```

### 添加新功能
1. 在相应的模块中添加功能
2. 编写单元测试
3. 更新文档
4. 运行完整测试套件

## 🤝 贡献指南

我们欢迎各种形式的贡献！

### 报告问题
- 使用GitHub Issues报告bug
- 提供详细的错误信息和复现步骤
- 包含系统环境信息

### 提交代码
1. Fork项目
2. 创建功能分支
3. 提交代码并编写测试
4. 创建Pull Request

### 代码规范
- 遵循PEP 8代码风格
- 添加类型注解
- 编写文档字符串
- 保持测试覆盖率

## 📞 支持与反馈

- 📧 邮箱: <EMAIL>
- 💬 讨论: GitHub Discussions
- 🐛 问题: GitHub Issues
- 📖 文档: 项目Wiki

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
