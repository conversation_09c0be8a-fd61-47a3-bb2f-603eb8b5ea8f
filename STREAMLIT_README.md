# 🤖 智能数据分析助手 - Streamlit前端界面

基于通义千问和PandasAI V2的智能数据分析平台，提供直观的Web界面进行自然语言数据查询。

## ✨ 主要功能

### 🔄 连续对话支持
- **会话历史维护**: 自动保存和加载聊天历史
- **上下文保持**: 在整个会话期间保持对话上下文
- **会话管理**: 支持清空历史、创建新会话

### 📁 文档上传与持久化存储
- **多格式支持**: CSV、Excel (xlsx/xls)、TXT、JSON
- **持久化存储**: 上传的文件自动保存到本地
- **文件管理**: 查看已上传文件列表，一键加载数据
- **上传反馈**: 实时显示上传状态和文件信息

### 🎨 简洁UI设计
- **直观布局**: 清晰的侧边栏和主界面分离
- **响应式设计**: 适配不同屏幕尺寸
- **快速操作**: 一键执行常用数据分析任务
- **状态指示**: 清晰的成功/错误/警告状态提示

### 🧠 智能数据分析
- **自然语言查询**: 支持中文和英文问题
- **实时分析**: 基于通义千问的智能代码生成
- **结果展示**: 清晰的分析结果显示
- **错误处理**: 完善的异常处理和用户提示

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果需要）
cd your-project-directory

# 安装依赖
pip install -r requirements_streamlit.txt
```

### 2. 配置API密钥

创建 `.env` 文件：

```bash
# .env
DASHSCOPE_API_KEY=your-dashscope-api-key-here
```

### 3. 启动应用

#### 方法一：使用启动脚本（推荐）
```bash
python run_streamlit.py
```

#### 方法二：直接启动
```bash
streamlit run streamlit_app.py
```

### 4. 访问应用

打开浏览器访问：`http://localhost:8501`

## 📖 使用指南

### 📊 数据上传
1. 在左侧边栏点击"选择数据文件"
2. 选择支持的文件格式（CSV、Excel、TXT、JSON）
3. 点击"上传文件"按钮
4. 上传成功后，文件会出现在"已上传文件"列表中

### 💬 智能对话
1. 上传数据后，在底部输入框中输入问题
2. 支持自然语言查询，例如：
   - "计算总销售额"
   - "哪个产品销量最高？"
   - "按价格排序显示所有产品"
   - "生成销售趋势图表"

### ⚡ 快速分析
数据加载后，可以使用快速分析按钮：
- **📈 数据概览**: 查看数据基本统计信息
- **🔍 缺失值检查**: 检查数据完整性
- **📊 数据类型**: 查看各列数据类型
- **🎯 相关性分析**: 分析数值列相关性

### 🗂️ 会话管理
- **🗑️ 清空历史**: 清除当前会话的所有对话记录
- **🔄 新会话**: 创建新的对话会话

## 📁 项目结构

```
project/
├── streamlit_app.py          # 主应用文件
├── config.py                 # 配置文件
├── run_streamlit.py          # 启动脚本
├── requirements_streamlit.txt # 依赖列表
├── .env                      # 环境变量（需要创建）
├── uploaded_files/           # 上传文件存储目录
├── chat_history/             # 聊天历史存储目录
├── charts/                   # 图表输出目录
└── temp/                     # 临时文件目录
```

## ⚙️ 配置选项

### 模型配置
在 `config.py` 中可以配置不同的通义千问模型：
- **qwen-turbo**: 快速模型，适合简单查询
- **qwen-plus**: 平衡模型，推荐使用（默认）
- **qwen-max**: 最强模型，适合复杂分析

### 文件限制
- 最大文件大小：100MB
- 支持格式：CSV、Excel、TXT、JSON

## 🔧 技术实现

### 核心技术栈
- **Streamlit**: Web界面框架
- **PandasAI V2**: 数据分析引擎
- **通义千问**: 大语言模型
- **Pandas**: 数据处理
- **Python-dotenv**: 环境变量管理

### 关键特性
- **会话状态管理**: 使用Streamlit session state
- **文件持久化**: 本地文件系统存储
- **错误处理**: 完善的异常捕获和用户提示
- **响应式UI**: 自适应布局设计

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 未找到DASHSCOPE_API_KEY环境变量
   ```
   **解决方案**: 检查 `.env` 文件是否存在且配置正确

2. **依赖缺失**
   ```
   ImportError: No module named 'streamlit'
   ```
   **解决方案**: 运行 `pip install -r requirements_streamlit.txt`

3. **文件上传失败**
   ```
   ❌ 文件保存失败
   ```
   **解决方案**: 检查文件权限和磁盘空间

4. **数据分析失败**
   ```
   ❌ 分析失败，请检查您的问题或数据格式
   ```
   **解决方案**: 
   - 检查数据格式是否正确
   - 尝试重新表述问题
   - 查看数据预览确认数据完整性

### 日志查看
- Streamlit日志会在终端显示
- 聊天历史保存在 `chat_history/` 目录
- 错误信息会在界面上显示

## 🔄 更新和维护

### 更新依赖
```bash
pip install -r requirements_streamlit.txt --upgrade
```

### 清理缓存
```bash
# 清理Streamlit缓存
streamlit cache clear

# 清理上传文件
rm -rf uploaded_files/*

# 清理聊天历史
rm -rf chat_history/*
```

## 📞 支持

如果遇到问题或需要帮助：
1. 检查本文档的故障排除部分
2. 查看终端输出的错误信息
3. 确认API密钥和网络连接正常
4. 验证数据文件格式是否支持

## 🎯 未来计划

- [ ] 支持更多数据源（数据库连接）
- [ ] 添加数据可视化图表
- [ ] 支持批量文件处理
- [ ] 添加用户认证功能
- [ ] 支持云存储集成
- [ ] 添加数据导出功能
