#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证脚本

验证应用配置的完整性和正确性。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.app_settings import get_config, reload_config
from core.utils.validators import validate_api_key


def validate_environment_variables():
    """验证环境变量"""
    print("🔍 验证环境变量...")
    
    required_vars = ['TONGYI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ 所有必需的环境变量都已设置")
        return True


def validate_api_keys():
    """验证API密钥"""
    print("\n🔑 验证API密钥...")
    
    config = get_config()
    
    # 验证通义千问API密钥
    if config.llm.tongyi_api_key:
        is_valid, error_msg = validate_api_key(config.llm.tongyi_api_key)
        if is_valid:
            print("✅ 通义千问API密钥格式有效")
            return True
        else:
            print(f"❌ 通义千问API密钥无效: {error_msg}")
            return False
    else:
        print("❌ 未设置通义千问API密钥")
        return False


def validate_directories():
    """验证目录结构"""
    print("\n📁 验证目录结构...")
    
    config = get_config()
    
    required_dirs = [
        config.data.uploaded_files_dir,
        config.data.chat_history_dir,
        config.data.charts_dir,
        config.data.cache_dir,
        config.data.logs_dir,
        config.data.metadata_config_dir,
    ]
    
    all_exist = True
    for directory in required_dirs:
        if directory.exists():
            print(f"✅ 目录存在: {directory}")
        else:
            print(f"❌ 目录不存在: {directory}")
            all_exist = False
    
    return all_exist


def validate_file_permissions():
    """验证文件权限"""
    print("\n🔒 验证文件权限...")
    
    config = get_config()
    
    # 检查关键目录的写权限
    test_dirs = [
        config.data.uploaded_files_dir,
        config.data.chat_history_dir,
        config.data.charts_dir,
        config.data.cache_dir,
        config.data.logs_dir,
    ]
    
    all_writable = True
    for directory in test_dirs:
        try:
            # 尝试创建测试文件
            test_file = directory / "test_write_permission.tmp"
            test_file.write_text("test")
            test_file.unlink()  # 删除测试文件
            print(f"✅ 目录可写: {directory}")
        except Exception as e:
            print(f"❌ 目录不可写: {directory} - {e}")
            all_writable = False
    
    return all_writable


def validate_config_values():
    """验证配置值"""
    print("\n⚙️ 验证配置值...")
    
    config = get_config()
    is_valid, errors = config.validate()
    
    if is_valid:
        print("✅ 所有配置值都有效")
        return True
    else:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False


def test_core_modules():
    """测试核心模块导入"""
    print("\n🧪 测试核心模块导入...")
    
    try:
        from core import LLMFactory, TongyiConfig
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        print("✅ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False


def generate_config_report():
    """生成配置报告"""
    print("\n📊 生成配置报告...")
    
    config = get_config()
    
    report = f"""
# 配置报告

## 应用信息
- 名称: {config.name}
- 版本: {config.version}
- 环境: {config.environment}
- 调试模式: {config.debug}

## LLM配置
- 模型: {config.llm.tongyi_model}
- Temperature: {config.llm.tongyi_temperature}
- Max Tokens: {config.llm.tongyi_max_tokens}
- 图表修复: {config.llm.enable_chart_fix}
- 元数据支持: {config.llm.enable_metadata}

## 数据配置
- 最大文件大小: {config.data.max_file_size_mb}MB
- 支持的文件类型: {', '.join(config.data.supported_file_types)}
- 自动清理数据: {config.data.auto_clean_data}

## 性能配置
- 启用缓存: {config.performance.enable_caching}
- 缓存TTL: {config.performance.cache_ttl_seconds}秒
- 最大并发请求: {config.performance.max_concurrent_requests}

## 安全配置
- 需要API密钥: {config.security.require_api_key}
- 允许文件操作: {config.security.allow_file_operations}
- 会话超时: {config.security.session_timeout_minutes}分钟
"""
    
    # 保存报告
    report_file = project_root / "config" / "config_report.md"
    report_file.write_text(report, encoding='utf-8')
    print(f"✅ 配置报告已保存到: {report_file}")


def main():
    """主函数"""
    print("🚀 开始配置验证...")
    print("=" * 60)
    
    # 重新加载配置
    reload_config()
    
    # 执行各项验证
    validations = [
        ("环境变量", validate_environment_variables),
        ("API密钥", validate_api_keys),
        ("目录结构", validate_directories),
        ("文件权限", validate_file_permissions),
        ("配置值", validate_config_values),
        ("核心模块", test_core_modules),
    ]
    
    results = {}
    for name, validator in validations:
        try:
            results[name] = validator()
        except Exception as e:
            print(f"❌ {name}验证时出错: {e}")
            results[name] = False
    
    # 生成配置报告
    try:
        generate_config_report()
    except Exception as e:
        print(f"⚠️ 生成配置报告时出错: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证结果总结:")
    
    all_passed = True
    for name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有验证都通过！应用配置正确。")
        print("💡 您可以运行以下命令启动应用:")
        print("   streamlit run streamlit_app.py")
        return 0
    else:
        print("\n⚠️ 部分验证失败，请检查配置。")
        print("💡 请参考 config/.env.example 文件配置环境变量")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
