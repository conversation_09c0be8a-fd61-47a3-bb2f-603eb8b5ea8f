#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实现安全的数据展示方案
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_secure_data_context(df, table_name="data"):
    """
    创建安全的数据上下文（无敏感数据泄露）
    
    Args:
        df: 原始数据框
        table_name: 表格名称
    
    Returns:
        str: 安全的数据上下文信息
    """
    
    # 基础结构信息
    basic_info = f"""数据形状: {df.shape}
列名: {list(df.columns)}

数据类型:
{df.dtypes.to_string()}"""
    
    # 数据质量摘要（无具体数值）
    quality_info = f"""
数据质量摘要:
- 总记录数: {len(df)}
- 缺失值情况: {'存在缺失值' if df.isnull().any().any() else '无缺失值'}
- 重复记录: {'存在重复' if df.duplicated().any() else '无重复'}"""
    
    # 列特征分析（无敏感信息）
    column_analysis = "\n列特征分析:"
    for col in df.columns:
        dtype = df[col].dtype
        if dtype in ['int64', 'float64']:
            unique_count = df[col].nunique()
            has_variation = df[col].std() > 0 if not df[col].isnull().all() else False
            column_analysis += f"""
  - {col}: 数值型列
    * 唯一值数量: {unique_count}
    * 数据分布: {'有变化' if has_variation else '无变化'}
    * 分析适用性: {'适合统计分析' if has_variation else '可能为常量'}"""
        else:
            unique_count = df[col].nunique()
            total_count = len(df)
            column_analysis += f"""
  - {col}: 分类型列
    * 唯一值数量: {unique_count}
    * 重复情况: {'存在重复值' if unique_count < total_count else '全部唯一'}
    * 分析适用性: {'适合分组分析' if unique_count < total_count else '适合标识符'}"""
    
    return basic_info + quality_info + column_analysis

def create_business_metadata_context(df, table_name="data"):
    """
    创建业务元数据上下文
    
    Args:
        df: 数据框
        table_name: 表格名称
    
    Returns:
        str: 业务元数据信息
    """
    
    # 这里应该从元数据管理系统获取，这里用示例
    metadata_context = f"""
业务元数据信息:
表格名称: {table_name}
业务领域: 根据列名推断的业务领域

列业务含义:"""
    
    # 基于列名推断业务含义（可以扩展为更智能的推断）
    for col in df.columns:
        col_lower = col.lower()
        if '销售额' in col or 'amount' in col_lower or '金额' in col:
            metadata_context += f"""
  - {col}: 核心收入指标，用于收入分析和趋势预测"""
        elif '地区' in col or 'region' in col_lower or '区域' in col:
            metadata_context += f"""
  - {col}: 地理维度，用于区域对比和地域分布分析"""
        elif '产品' in col or 'product' in col_lower:
            metadata_context += f"""
  - {col}: 产品维度，用于产品分析和品类对比"""
        elif '销量' in col or 'quantity' in col_lower or '数量' in col:
            metadata_context += f"""
  - {col}: 销售规模指标，用于销量分析和库存管理"""
        elif '日期' in col or 'date' in col_lower or 'time' in col_lower:
            metadata_context += f"""
  - {col}: 时间维度，用于时间序列分析和趋势分析"""
        elif '员工' in col or 'staff' in col_lower or '销售员' in col:
            metadata_context += f"""
  - {col}: 人员维度，用于绩效分析和团队管理"""
        else:
            metadata_context += f"""
  - {col}: 业务属性，用于数据分析和分类统计"""
    
    # 推荐分析方法
    metadata_context += f"""

推荐分析方法:
- 描述性统计分析
- 分组对比分析
- 趋势分析（如有时间列）
- 相关性分析（数值列间）"""
    
    return metadata_context

def test_secure_approach():
    """测试安全方案"""
    print("🧪 测试安全的数据展示方案...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
        '销量': [5, 3, 8, 15, 6],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '销售员': ['张三', '李四', '王五', '赵六', '张三']
    })
    
    # 生成安全的上下文
    secure_context = create_secure_data_context(test_data, "sales_data")
    business_context = create_business_metadata_context(test_data, "sales_data")
    
    full_context = secure_context + business_context
    
    print("✅ 安全上下文生成成功")
    print(f"📊 信息长度: {len(full_context)} 字符")
    
    # 安全性检查
    sensitive_terms = ['张三', '李四', '8500', '6200', '北京', '上海']
    found_sensitive = [term for term in sensitive_terms if term in full_context]
    
    if found_sensitive:
        print(f"❌ 发现敏感信息: {found_sensitive}")
    else:
        print("✅ 无敏感信息泄露")
    
    # 显示生成的安全上下文
    print("\n📋 生成的安全上下文:")
    print("=" * 60)
    print(full_context)
    print("=" * 60)
    
    return len(found_sensitive) == 0

if __name__ == "__main__":
    print("🔐 实施安全的数据展示方案")
    print("=" * 60)
    
    success = test_secure_approach()
    
    if success:
        print("\n🎉 安全方案测试成功！")
        print("\n💡 实施建议:")
        print("1. 立即移除所有原始数据样例")
        print("2. 实施这个安全的上下文生成方案")
        print("3. 保留数据结构和业务元数据")
        print("4. 定期审查确保无敏感信息泄露")
    else:
        print("\n⚠️ 需要进一步优化安全措施")
    
    print(f"\n🎯 结论: 您的担忧完全正确！")
    print(f"   应该立即移除敏感的样例数据，")
    print(f"   改用安全的元数据增强方案。")
