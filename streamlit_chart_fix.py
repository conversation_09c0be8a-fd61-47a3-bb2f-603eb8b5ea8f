#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit图表问题修复工具
专门解决销售额_start、销售额_end字段和无穷大值导致的图表渲染问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import re
import json
from datetime import datetime
from pathlib import Path

class StreamlitChartFixer:
    """Streamlit图表问题修复器"""
    
    @staticmethod
    def detect_problematic_fields(df):
        """检测问题字段"""
        problematic_fields = []
        
        # 检查销售额相关的异常字段
        for col in df.columns:
            col_str = str(col)
            if any(pattern in col_str for pattern in ['_start', '_end', '_begin', '_finish']):
                if '销售额' in col_str or '销售' in col_str or '金额' in col_str:
                    problematic_fields.append(col)
        
        return problematic_fields
    
    @staticmethod
    def detect_infinite_values(df):
        """检测无穷大值"""
        infinite_columns = {}
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            inf_count = np.isinf(df[col]).sum()
            if inf_count > 0:
                infinite_columns[col] = {
                    'positive_inf': np.isposinf(df[col]).sum(),
                    'negative_inf': np.isneginf(df[col]).sum(),
                    'total_inf': inf_count
                }
        
        return infinite_columns
    
    @staticmethod
    def clean_dataframe(df):
        """清理DataFrame"""
        cleaned_df = df.copy()
        
        # 1. 移除问题字段
        problematic_fields = StreamlitChartFixer.detect_problematic_fields(cleaned_df)
        if problematic_fields:
            cleaned_df = cleaned_df.drop(columns=problematic_fields)
            st.info(f"已移除问题字段: {problematic_fields}")
        
        # 2. 清理无穷大值
        numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if np.isinf(cleaned_df[col]).any():
                cleaned_df[col] = cleaned_df[col].replace([np.inf, -np.inf], np.nan)
                cleaned_df[col] = cleaned_df[col].fillna(0)
        
        # 3. 清理列名
        original_columns = list(cleaned_df.columns)
        cleaned_df.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in cleaned_df.columns]
        
        # 4. 处理重复索引
        if cleaned_df.index.duplicated().any():
            cleaned_df = cleaned_df.reset_index(drop=True)
        
        return cleaned_df
    
    @staticmethod
    def validate_chart_data(data):
        """验证图表数据"""
        if data is None or (hasattr(data, 'empty') and data.empty):
            return False, "数据为空"
        
        if isinstance(data, pd.Series):
            if np.isinf(data).any():
                return False, "包含无穷大值"
            if data.isnull().all():
                return False, "全为空值"
            if (data == 0).all():
                return False, "全为零值"
        
        return True, "数据正常"

def main():
    """主函数"""
    st.title("🔧 Streamlit图表问题修复工具")
    st.markdown("专门解决销售额_start、销售额_end字段和无穷大值导致的图表渲染问题")
    
    # 检查当前数据
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        st.subheader("📊 当前数据状态")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("数据行数", len(df))
        with col2:
            st.metric("数据列数", len(df.columns))
        with col3:
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            st.metric("数值列数", len(numeric_cols))
        
        # 问题检测
        st.subheader("🔍 问题检测")
        
        # 检测问题字段
        problematic_fields = StreamlitChartFixer.detect_problematic_fields(df)
        if problematic_fields:
            st.error(f"❌ 发现问题字段: {problematic_fields}")
            st.write("这些字段可能导致图表渲染失败")
        else:
            st.success("✅ 未发现问题字段")
        
        # 检测无穷大值
        infinite_columns = StreamlitChartFixer.detect_infinite_values(df)
        if infinite_columns:
            st.error("❌ 发现无穷大值:")
            for col, info in infinite_columns.items():
                st.write(f"  - {col}: 正无穷={info['positive_inf']}, 负无穷={info['negative_inf']}")
        else:
            st.success("✅ 未发现无穷大值")
        
        # 修复按钮
        st.subheader("🔧 数据修复")
        
        if st.button("🚨 立即修复所有问题", type="primary"):
            with st.spinner("正在修复数据问题..."):
                try:
                    # 清理数据
                    cleaned_df = StreamlitChartFixer.clean_dataframe(df)
                    
                    # 更新session_state
                    st.session_state.current_data = cleaned_df
                    
                    # 清理缓存
                    st.cache_data.clear()
                    st.cache_resource.clear()
                    
                    st.success("🎉 修复完成！")
                    st.info("💡 建议刷新页面以确保修复生效")
                    
                    # 显示修复后的数据状态
                    st.subheader("📈 修复后数据状态")
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("修复后行数", len(cleaned_df))
                    with col2:
                        st.metric("修复后列数", len(cleaned_df.columns))
                    with col3:
                        numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
                        st.metric("修复后数值列数", len(numeric_cols))
                    
                    # 显示修复详情
                    with st.expander("📋 修复详情"):
                        st.write("**原始列名:**", list(df.columns))
                        st.write("**修复后列名:**", list(cleaned_df.columns))
                        
                        if problematic_fields:
                            st.write("**已移除的问题字段:**", problematic_fields)
                        
                        if infinite_columns:
                            st.write("**已清理的无穷大值列:**", list(infinite_columns.keys()))
                    
                    if st.button("🔄 刷新页面"):
                        st.rerun()
                        
                except Exception as e:
                    st.error(f"❌ 修复失败: {e}")
        
        # 数据预览
        with st.expander("📊 数据预览"):
            st.dataframe(df.head(10))
        
        # 列详情
        with st.expander("📋 列详情"):
            for col in df.columns:
                col_info = f"**{col}** ({df[col].dtype})"
                if col in problematic_fields:
                    col_info += " ❌ 问题字段"
                elif col in infinite_columns:
                    col_info += f" ⚠️ 包含{infinite_columns[col]['total_inf']}个无穷大值"
                else:
                    col_info += " ✅ 正常"
                
                st.write(col_info)
    
    else:
        st.info("⚠️ 当前没有加载数据")
        st.write("请先在主应用中上传数据文件")
    
    # 使用说明
    with st.expander("📖 使用说明"):
        st.markdown("""
        ### 🎯 工具功能
        
        1. **问题检测**: 自动检测可能导致图表渲染失败的字段和数值
        2. **数据清理**: 移除问题字段，清理无穷大值和异常数据
        3. **缓存清理**: 清理Streamlit缓存，确保修复生效
        
        ### ⚠️ 常见问题
        
        - **销售额_start/销售额_end字段**: 这些字段通常包含无穷大值，会导致Vega-Lite图表渲染失败
        - **无穷大值**: Infinity和-Infinity值会导致图表范围计算错误
        - **特殊字符列名**: 包含特殊字符的列名可能导致渲染冲突
        
        ### 💡 使用建议
        
        1. 在遇到图表显示问题时立即使用此工具
        2. 修复后建议刷新页面确保生效
        3. 定期检查数据质量，预防问题发生
        """)

if __name__ == "__main__":
    main()
