#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的上下文管理器 - 解决提示词复杂性和信息冲突问题
"""

import streamlit as st
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, asdict

@dataclass
class OptimizedConversationContext:
    """优化的对话上下文数据结构"""
    current_instruction: str
    has_reference: bool
    reference_type: str  # 'continuation', 'modification', 'comparison', 'extension'
    confidence: float
    key_variables: List[str]  # 从历史代码中提取的关键变量
    key_operations: List[str]  # 关键操作类型
    business_context: str  # 业务上下文摘要
    guidance: List[str]  # 智能指导原则

class OptimizedContextManager:
    """
    优化的上下文管理器
    
    核心改进：
    1. 精简历史上下文，避免完整代码粘贴
    2. 结构化提示词，明确区分不同信息类型
    3. 智能指导原则替代强制模板
    4. 改进LLM响应解析和验证
    """
    
    def __init__(self, enable_logging: bool = True):
        self.logger = get_app_logger() if enable_logging else None
        self.summary_trigger_rounds = 4
        self.max_recent_rounds = 2  # 减少到2轮，避免信息过载
        self.max_context_length = 6000  # 适当减少上下文长度
        self._init_context_state()
    
    def build_optimized_context_for_llm(self, current_instruction: str) -> OptimizedConversationContext:
        """
        构建优化的LLM上下文
        
        核心改进：
        1. 只提取关键信息，不包含完整代码
        2. 明确区分引用类型和置信度
        3. 提供结构化的指导原则
        """
        context_state = st.session_state.context_manager
        recent_rounds = context_state['conversation_rounds'][-self.max_recent_rounds:]
        
        # 1. 检测引用关系和类型
        reference_info = self._detect_reference_with_confidence(current_instruction, recent_rounds)
        
        # 2. 提取关键变量和操作
        key_variables, key_operations = self._extract_key_elements(recent_rounds)
        
        # 3. 生成业务上下文摘要
        business_context = self._generate_business_context_summary(recent_rounds, current_instruction)
        
        # 4. 生成智能指导原则
        guidance = self._generate_smart_guidance(reference_info, key_variables, key_operations)
        
        return OptimizedConversationContext(
            current_instruction=current_instruction,
            has_reference=reference_info['has_reference'],
            reference_type=reference_info['type'],
            confidence=reference_info['confidence'],
            key_variables=key_variables,
            key_operations=key_operations,
            business_context=business_context,
            guidance=guidance
        )
    
    def _detect_reference_with_confidence(self, instruction: str, recent_rounds: List[Dict]) -> Dict[str, Any]:
        """
        改进的引用检测，提供更准确的置信度
        """
        instruction_lower = instruction.lower()
        
        # 语义模式匹配（更精确的模式）
        reference_patterns = {
            'continuation': {
                'patterns': [
                    r'(在.*基础上|基于.*|根据.*|参考.*)',
                    r'(进一步.*|深入.*|详细.*|具体.*)',
                    r'(接着.*|然后.*|继续.*)'
                ],
                'confidence_base': 0.9
            },
            'modification': {
                'patterns': [
                    r'(修改.*|调整.*|改变.*|优化.*)',
                    r'(重新.*|再次.*|重做.*)',
                    r'(换成.*|改为.*|变成.*)'
                ],
                'confidence_base': 0.95
            },
            'comparison': {
                'patterns': [
                    r'(对比.*|比较.*|对照.*)',
                    r'(差异.*|区别.*|不同.*)',
                    r'(相比.*|与.*比较)'
                ],
                'confidence_base': 0.9
            },
            'extension': {
                'patterns': [
                    r'(同时.*|另外.*|此外.*|还要.*)',
                    r'(加上.*|增加.*|补充.*)',
                    r'(以及.*|和.*一起)'
                ],
                'confidence_base': 0.8
            }
        }
        
        best_type = 'independent'
        max_confidence = 0.0
        
        for ref_type, config in reference_patterns.items():
            confidence = 0.0
            pattern_matches = 0
            
            for pattern in config['patterns']:
                if re.search(pattern, instruction_lower):
                    pattern_matches += 1
            
            if pattern_matches > 0:
                confidence = config['confidence_base'] * (pattern_matches / len(config['patterns']))
                
                # 上下文验证：检查是否真的有可引用的内容
                if recent_rounds and self._has_referenceable_content(recent_rounds):
                    confidence += 0.1  # 奖励分
                
                if confidence > max_confidence:
                    max_confidence = confidence
                    best_type = ref_type
        
        return {
            'has_reference': max_confidence >= 0.5,  # 提高阈值
            'type': best_type,
            'confidence': max_confidence
        }
    
    def _extract_key_elements(self, recent_rounds: List[Dict]) -> Tuple[List[str], List[str]]:
        """
        从历史对话中提取关键变量和操作，而不是完整代码
        """
        key_variables = []
        key_operations = []
        
        for round_data in recent_rounds:
            code = round_data.get('code', '')
            if not code:
                continue
            
            # 提取变量名（更精确的正则）
            variables = re.findall(r'(\w+)\s*=\s*(?!.*def\s|.*class\s)', code)
            key_variables.extend(variables)
            
            # 提取关键操作（更详细的分类）
            operations = []
            if 'groupby' in code.lower():
                # 提取分组字段
                groupby_fields = re.findall(r'groupby\([\'"]([^\'"]+)[\'"]', code)
                if groupby_fields:
                    operations.append(f"按{groupby_fields[0]}分组")
                else:
                    operations.append("数据分组")
            
            if '.sum()' in code:
                operations.append("求和计算")
            if '.mean()' in code:
                operations.append("平均值计算")
            if '.count()' in code:
                operations.append("计数统计")
            if 'chart' in code.lower() or 'plot' in code.lower():
                operations.append("图表可视化")
            if 'dataframe' in code.lower() or 'st.dataframe' in code:
                operations.append("表格展示")
            
            key_operations.extend(operations)
        
        # 去重并保持顺序
        key_variables = list(dict.fromkeys(key_variables))
        key_operations = list(dict.fromkeys(key_operations))
        
        return key_variables, key_operations
    
    def _generate_business_context_summary(self, recent_rounds: List[Dict], current_instruction: str) -> str:
        """
        生成简洁的业务上下文摘要
        """
        if not recent_rounds:
            return "这是一个新的分析任务。"
        
        # 提取业务主题
        topics = []
        for round_data in recent_rounds:
            user_msg = round_data.get('user_message', '')
            # 简单的主题提取
            if '地区' in user_msg:
                topics.append('地区分析')
            if '销售' in user_msg:
                topics.append('销售分析')
            if '产品' in user_msg:
                topics.append('产品分析')
            if '销售员' in user_msg:
                topics.append('销售员分析')
        
        topics = list(set(topics))
        
        if topics:
            return f"用户正在进行{', '.join(topics)}，当前希望{current_instruction}"
        else:
            return f"用户正在进行数据分析，当前希望{current_instruction}"
    
    def _generate_smart_guidance(self, reference_info: Dict, key_variables: List[str], 
                               key_operations: List[str]) -> List[str]:
        """
        生成智能指导原则，替代强制模板
        """
        guidance = []
        
        if reference_info['has_reference']:
            ref_type = reference_info['type']
            
            if ref_type == 'continuation':
                guidance.append("🔗 **延续性分析**: 用户希望基于之前的分析结果进行深入分析")
                if key_variables:
                    guidance.append(f"💡 **变量复用**: 可以直接使用已存在的变量: {', '.join(key_variables[:3])}")
                guidance.append("📋 **分析建议**: 在现有分析基础上增加新的维度或深度")
                
            elif ref_type == 'modification':
                guidance.append("🔧 **修改优化**: 用户希望调整或改进之前的分析")
                guidance.append("💡 **改进方向**: 保持核心逻辑，优化展示方式或分析角度")
                
            elif ref_type == 'comparison':
                guidance.append("⚖️ **对比分析**: 用户希望进行比较分析")
                guidance.append("💡 **对比建议**: 使用相同的分析方法对不同维度进行对比")
                
            elif ref_type == 'extension':
                guidance.append("➕ **扩展分析**: 用户希望在现有基础上增加新内容")
                guidance.append("💡 **扩展建议**: 保持现有分析，同时添加新的分析维度")
        else:
            guidance.append("🆕 **独立分析**: 这是一个新的分析任务")
            guidance.append("💡 **分析建议**: 从数据探索开始，提供全面的分析")
        
        # 添加技术指导
        guidance.append("🛠️ **技术要求**:")
        guidance.append("  - 确保代码能够独立运行")
        guidance.append("  - 使用Streamlit组件展示结果")
        guidance.append("  - 代码简洁高效，注重可读性")
        
        return guidance
    
    def _has_referenceable_content(self, recent_rounds: List[Dict]) -> bool:
        """
        检查是否有可引用的内容
        """
        for round_data in recent_rounds:
            if round_data.get('code') and round_data.get('execution_result', {}).get('success'):
                return True
        return False
    
    def build_structured_prompt(self, context: OptimizedConversationContext, 
                              data_context: str, metadata: Optional[Dict] = None) -> str:
        """
        构建结构化的提示词
        
        核心改进：
        1. 清晰的信息分区
        2. 智能指导原则替代强制模板
        3. 避免信息冲突
        """
        prompt_parts = [
            "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
            "",
            "=" * 50,
            "📊 **数据信息**",
            "=" * 50,
            data_context,
            ""
        ]
        
        # 添加元数据（如果有）
        if metadata:
            prompt_parts.extend([
                "=" * 50,
                "🏷️ **数据元数据**", 
                "=" * 50,
                self._format_metadata_brief(metadata),
                ""
            ])
        
        # 添加业务上下文（精简版）
        if context.has_reference:
            prompt_parts.extend([
                "=" * 50,
                "🔄 **对话上下文**",
                "=" * 50,
                f"**业务背景**: {context.business_context}",
                f"**引用类型**: {context.reference_type} (置信度: {context.confidence:.2f})",
                ""
            ])
            
            if context.key_variables:
                prompt_parts.append(f"**可用变量**: {', '.join(context.key_variables)}")
            if context.key_operations:
                prompt_parts.append(f"**历史操作**: {', '.join(context.key_operations)}")
            prompt_parts.append("")
        
        # 添加当前任务
        prompt_parts.extend([
            "=" * 50,
            "🎯 **当前任务**",
            "=" * 50,
            f"**用户指令**: {context.current_instruction}",
            ""
        ])
        
        # 添加智能指导原则
        prompt_parts.extend([
            "=" * 50,
            "💡 **分析指导**",
            "=" * 50
        ])
        prompt_parts.extend(context.guidance)
        prompt_parts.extend([
            "",
            "=" * 50,
            "📝 **请生成Python代码**",
            "=" * 50,
            "请基于以上信息生成相应的Python代码，确保代码能够独立运行并使用Streamlit组件展示结果。"
        ])
        
        return "\n".join(prompt_parts)
    
    def _format_metadata_brief(self, metadata: Dict) -> str:
        """简化的元数据格式化"""
        if not metadata:
            return ""
        
        brief_parts = []
        if 'columns' in metadata:
            brief_parts.append(f"列数: {len(metadata['columns'])}")
        if 'shape' in metadata:
            brief_parts.append(f"数据形状: {metadata['shape']}")
        
        return " | ".join(brief_parts)

class OptimizedLLMResponseProcessor:
    """
    优化的LLM响应处理器

    核心改进：
    1. 更精确的代码提取
    2. 引用性代码验证
    3. 代码质量检查
    """

    def __init__(self, enable_logging: bool = True):
        self.logger = get_app_logger() if enable_logging else None

    def extract_and_validate_code(self, llm_response: str, context: OptimizedConversationContext) -> Tuple[str, bool, str]:
        """
        从LLM响应中提取并验证代码

        Args:
            llm_response: LLM的完整响应
            context: 对话上下文

        Returns:
            (提取的代码, 是否有效, 错误信息)
        """
        try:
            # 1. 提取代码块
            extracted_code = self._extract_code_blocks(llm_response)

            if not extracted_code:
                return "", False, "未找到有效的代码块"

            # 2. 验证引用性代码
            if context.has_reference:
                validation_result = self._validate_reference_code(extracted_code, context)
                if not validation_result['valid']:
                    # 尝试修复代码
                    fixed_code = self._fix_reference_code(extracted_code, context, validation_result['issues'])
                    if fixed_code:
                        extracted_code = fixed_code
                        if self.logger:
                            self.logger.info("引用性代码已自动修复")
                    else:
                        return extracted_code, False, f"引用验证失败: {validation_result['issues']}"

            # 3. 基础代码质量检查
            quality_check = self._check_code_quality(extracted_code)
            if not quality_check['valid']:
                return extracted_code, False, f"代码质量检查失败: {quality_check['issues']}"

            return extracted_code, True, ""

        except Exception as e:
            error_msg = f"代码提取和验证失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return "", False, error_msg

    def _extract_code_blocks(self, response: str) -> str:
        """
        改进的代码块提取
        """
        # 优先提取```python代码块
        python_pattern = r'```python\s*\n(.*?)\n```'
        python_matches = re.findall(python_pattern, response, re.DOTALL)

        if python_matches:
            # 如果有多个代码块，选择最长的（通常是主要代码）
            return max(python_matches, key=len).strip()

        # 备选：提取```代码块
        general_pattern = r'```\s*\n(.*?)\n```'
        general_matches = re.findall(general_pattern, response, re.DOTALL)

        if general_matches:
            return max(general_matches, key=len).strip()

        # 最后尝试：查找看起来像Python代码的行
        lines = response.split('\n')
        code_lines = []
        in_code_section = False

        for line in lines:
            stripped = line.strip()
            if any(stripped.startswith(keyword) for keyword in ['import ', 'from ', 'df.', 'st.', '#']):
                in_code_section = True
                code_lines.append(line)
            elif in_code_section and (stripped.startswith(' ') or stripped == ''):
                code_lines.append(line)
            elif in_code_section and stripped:
                # 可能是代码的延续
                if any(char in stripped for char in ['=', '(', ')', '[', ']', '.']):
                    code_lines.append(line)
                else:
                    break

        return '\n'.join(code_lines).strip() if code_lines else ""

    def _validate_reference_code(self, code: str, context: OptimizedConversationContext) -> Dict[str, Any]:
        """
        验证引用性代码是否正确使用了历史变量
        """
        issues = []

        if context.reference_type == 'continuation' and context.key_variables:
            # 检查是否复用了关键变量
            code_lower = code.lower()
            used_variables = []

            for var in context.key_variables:
                if var.lower() in code_lower:
                    used_variables.append(var)

            if not used_variables:
                issues.append(f"延续性分析应该复用历史变量: {', '.join(context.key_variables[:3])}")

            # 检查是否有重复计算
            if 'groupby' in code_lower and any('groupby' in op for op in context.key_operations):
                # 检查是否在重复相同的分组操作
                if not any(var in code_lower for var in context.key_variables):
                    issues.append("检测到可能的重复计算，建议复用已有的分组结果")

        elif context.reference_type == 'modification':
            # 修改类型应该有新的逻辑
            if not self._has_new_logic(code, context):
                issues.append("修改类型的分析应该包含新的逻辑或改进")

        return {
            'valid': len(issues) == 0,
            'issues': '; '.join(issues)
        }

    def _fix_reference_code(self, code: str, context: OptimizedConversationContext, issues: str) -> Optional[str]:
        """
        尝试自动修复引用性代码
        """
        if context.reference_type == 'continuation' and context.key_variables:
            # 简单的修复：在代码开头添加变量检查和复用逻辑
            key_var = context.key_variables[0]  # 使用第一个关键变量

            fix_template = f"""# 复用历史分析结果
if '{key_var}' not in locals():
    # 如果变量不存在，重新计算（备用方案）
    {key_var} = df.groupby('地区')['销售额'].sum().reset_index()

# 基于历史结果的进一步分析
{code}"""

            return fix_template

        return None

    def _has_new_logic(self, code: str, context: OptimizedConversationContext) -> bool:
        """
        检查代码是否包含新的逻辑
        """
        # 简单检查：是否有新的操作或不同的分组方式
        code_lower = code.lower()

        # 检查是否有新的操作
        new_operations = ['mean()', 'count()', 'max()', 'min()', 'std()']
        has_new_ops = any(op in code_lower for op in new_operations)

        # 检查是否有不同的分组
        if 'groupby' in code_lower:
            # 提取分组字段
            groupby_matches = re.findall(r'groupby\([\'"]([^\'"]+)[\'"]', code)
            if groupby_matches:
                new_groupby = groupby_matches[0]
                # 检查是否与历史操作不同
                historical_groupby = any('地区' in op for op in context.key_operations)
                if new_groupby != '地区' or not historical_groupby:
                    return True

        return has_new_ops

    def _check_code_quality(self, code: str) -> Dict[str, Any]:
        """
        基础代码质量检查
        """
        issues = []

        # 检查基本语法结构
        if not code.strip():
            issues.append("代码为空")
            return {'valid': False, 'issues': '; '.join(issues)}

        # 检查是否有基本的Python结构
        if not any(char in code for char in ['=', '(', ')']):
            issues.append("代码结构异常")

        # 检查是否有Streamlit组件
        if 'st.' not in code:
            issues.append("缺少Streamlit展示组件")

        # 检查是否使用了df变量
        if 'df' not in code and 'data' not in code:
            issues.append("未使用数据变量df")

        return {
            'valid': len(issues) == 0,
            'issues': '; '.join(issues)
        }

# 使用示例
def create_optimized_analysis_pipeline():
    """
    创建优化的分析流水线
    """
    context_manager = OptimizedContextManager()
    response_processor = OptimizedLLMResponseProcessor()

    def analyze_with_optimized_context(instruction: str, data_context: str, metadata: Optional[Dict] = None):
        # 1. 构建优化的上下文
        context = context_manager.build_optimized_context_for_llm(instruction)

        # 2. 构建结构化提示词
        structured_prompt = context_manager.build_structured_prompt(context, data_context, metadata)

        # 3. 调用LLM（这里需要集成实际的LLM调用）
        # llm_response = call_llm(structured_prompt)

        # 4. 提取和验证代码
        # extracted_code, is_valid, error_msg = response_processor.extract_and_validate_code(llm_response, context)

        return structured_prompt, context  # 返回用于测试

    return analyze_with_optimized_context
