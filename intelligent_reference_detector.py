#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能引用检测器
基于语义理解而非硬编码关键词的引用检测系统
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ReferenceIntent:
    """引用意图数据类"""
    intent_type: str  # 'continuation', 'modification', 'comparison', 'extension'
    confidence: float  # 置信度 0-1
    referenced_elements: List[str]  # 引用的元素类型
    action_type: str  # 'analyze', 'modify', 'compare', 'extend'

class IntelligentReferenceDetector:
    """智能引用检测器"""
    
    def __init__(self):
        # 语义模式定义
        self.semantic_patterns = {
            # 延续性分析模式
            'continuation': {
                'patterns': [
                    r'(在.*基础上|基于.*|根据.*|参考.*|延续.*|继续.*|接着.*)',
                    r'(结合.*结果|利用.*分析|使用.*数据)',
                    r'(进一步.*|深入.*|详细.*|具体.*)',
                ],
                'confidence_base': 0.8,
                'action_indicators': ['分析', '研究', '探索', '查看', '统计']
            },
            
            # 修改性模式
            'modification': {
                'patterns': [
                    r'(修改.*|调整.*|改变.*|优化.*)',
                    r'(重新.*|再次.*|重做.*)',
                    r'(换成.*|改为.*|变成.*)'
                ],
                'confidence_base': 0.9,
                'action_indicators': ['修改', '调整', '改变', '优化', '重新']
            },
            
            # 对比性模式
            'comparison': {
                'patterns': [
                    r'(对比.*|比较.*|对照.*)',
                    r'(差异.*|区别.*|不同.*)',
                    r'(相比.*|与.*比较)'
                ],
                'confidence_base': 0.85,
                'action_indicators': ['对比', '比较', '对照']
            },
            
            # 扩展性模式
            'extension': {
                'patterns': [
                    r'(同时.*|另外.*|此外.*|还要.*)',
                    r'(加上.*|增加.*|补充.*)',
                    r'(以及.*|和.*一起|结合.*)'
                ],
                'confidence_base': 0.7,
                'action_indicators': ['增加', '补充', '扩展', '添加']
            }
        }
        
        # 引用对象识别模式
        self.reference_object_patterns = {
            'analysis': {
                'keywords': ['分析', '结果', '数据', '统计', '计算'],
                'context_clues': ['销售额', '地区', '产品', '销量', '趋势']
            },
            'chart': {
                'keywords': ['图', '图表', '图形', '可视化', '展示'],
                'context_clues': ['条形图', '柱状图', '饼图', '折线图', '散点图']
            },
            'table': {
                'keywords': ['表', '表格', '数据表', '列表'],
                'context_clues': ['行', '列', '数据框', 'dataframe']
            },
            'variable': {
                'keywords': ['变量', '数据', '值', '结果'],
                'context_clues': ['sales', 'region', 'product', 'amount']
            }
        }
    
    def detect_reference_intent(self, instruction: str, conversation_history: List[Dict]) -> Optional[ReferenceIntent]:
        """
        检测引用意图
        
        Args:
            instruction: 用户指令
            conversation_history: 对话历史
            
        Returns:
            ReferenceIntent对象或None
        """
        instruction_lower = instruction.lower()
        
        # 1. 语义模式匹配
        best_intent = None
        max_confidence = 0.0
        
        for intent_type, config in self.semantic_patterns.items():
            confidence = self._calculate_pattern_confidence(instruction_lower, config)
            
            if confidence > max_confidence:
                max_confidence = confidence
                best_intent = intent_type
        
        # 2. 如果没有明显的语义模式，尝试上下文推理
        if max_confidence < 0.3:
            context_confidence = self._analyze_context_clues(instruction_lower, conversation_history)
            if context_confidence > max_confidence:
                max_confidence = context_confidence
                best_intent = 'continuation'  # 默认为延续性
        
        # 3. 如果置信度足够高，构建引用意图
        if max_confidence >= 0.3:
            referenced_elements = self._identify_referenced_elements(instruction_lower, conversation_history)
            action_type = self._identify_action_type(instruction_lower)
            
            return ReferenceIntent(
                intent_type=best_intent,
                confidence=max_confidence,
                referenced_elements=referenced_elements,
                action_type=action_type
            )
        
        return None
    
    def _calculate_pattern_confidence(self, instruction: str, config: Dict) -> float:
        """计算模式匹配置信度"""
        confidence = 0.0
        base_confidence = config['confidence_base']
        
        # 检查语义模式
        pattern_matches = 0
        for pattern in config['patterns']:
            if re.search(pattern, instruction):
                pattern_matches += 1
        
        if pattern_matches > 0:
            confidence += base_confidence * (pattern_matches / len(config['patterns']))
        
        # 检查动作指示词
        action_matches = 0
        for action in config['action_indicators']:
            if action in instruction:
                action_matches += 1
        
        if action_matches > 0:
            confidence += 0.2 * (action_matches / len(config['action_indicators']))
        
        return min(confidence, 1.0)
    
    def _analyze_context_clues(self, instruction: str, conversation_history: List[Dict]) -> float:
        """分析上下文线索"""
        if not conversation_history:
            return 0.0
        
        confidence = 0.0
        
        # 检查是否提到了历史分析中的关键概念
        last_round = conversation_history[-1] if conversation_history else None
        if last_round and last_round.get('code'):
            last_code = last_round['code'].lower()
            
            # 提取上一轮的关键概念
            key_concepts = self._extract_key_concepts(last_code)
            
            # 检查当前指令是否涉及这些概念
            concept_matches = 0
            for concept in key_concepts:
                if concept in instruction:
                    concept_matches += 1
            
            if concept_matches > 0:
                confidence = 0.6 * (concept_matches / max(len(key_concepts), 1))
        
        # 检查时间相关的隐含表达
        implicit_time_patterns = [
            r'(然后|接下来|下一步|现在|这次)',
            r'(同样|类似|相同|一样)',
            r'(另外|其他|别的|不同)'
        ]
        
        for pattern in implicit_time_patterns:
            if re.search(pattern, instruction):
                confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _extract_key_concepts(self, code: str) -> List[str]:
        """从代码中提取关键概念"""
        concepts = []
        
        # 提取变量名
        var_pattern = r'(\w+)\s*='
        variables = re.findall(var_pattern, code)
        concepts.extend(variables)
        
        # 提取分组字段
        groupby_pattern = r'groupby\([\'"]([^\'"]+)[\'"]\)'
        groupby_fields = re.findall(groupby_pattern, code)
        concepts.extend(groupby_fields)
        
        # 提取聚合字段
        agg_pattern = r'\[[\'"]([^\'"]+)[\'"]\]'
        agg_fields = re.findall(agg_pattern, code)
        concepts.extend(agg_fields)
        
        return list(set(concepts))
    
    def _identify_referenced_elements(self, instruction: str, conversation_history: List[Dict]) -> List[str]:
        """识别引用的元素类型"""
        referenced = []
        
        for element_type, config in self.reference_object_patterns.items():
            # 检查关键词
            keyword_matches = sum(1 for keyword in config['keywords'] if keyword in instruction)
            
            # 检查上下文线索
            context_matches = sum(1 for clue in config['context_clues'] if clue in instruction)
            
            # 检查历史对话中是否有相关内容
            history_relevance = self._check_history_relevance(element_type, conversation_history)
            
            total_score = keyword_matches + context_matches * 0.5 + history_relevance
            
            if total_score >= 1.0:
                referenced.append(element_type)
        
        return referenced
    
    def _check_history_relevance(self, element_type: str, conversation_history: List[Dict]) -> float:
        """检查历史对话的相关性"""
        if not conversation_history:
            return 0.0
        
        last_round = conversation_history[-1]
        if not last_round.get('code'):
            return 0.0
        
        code = last_round['code'].lower()
        
        relevance_indicators = {
            'analysis': ['groupby', 'sum', 'mean', 'count', 'describe'],
            'chart': ['chart', 'plot', 'bar_chart', 'line_chart', 'pie_chart'],
            'table': ['dataframe', 'table', 'reset_index', 'head', 'tail'],
            'variable': ['=', 'df[', 'iloc', 'loc']
        }
        
        indicators = relevance_indicators.get(element_type, [])
        matches = sum(1 for indicator in indicators if indicator in code)
        
        return min(matches / len(indicators), 1.0) if indicators else 0.0
    
    def _identify_action_type(self, instruction: str) -> str:
        """识别动作类型"""
        action_patterns = {
            'analyze': ['分析', '研究', '探索', '查看', '统计', '计算'],
            'modify': ['修改', '调整', '改变', '优化', '重新'],
            'compare': ['对比', '比较', '对照', '差异'],
            'extend': ['增加', '补充', '扩展', '添加', '结合']
        }
        
        for action_type, keywords in action_patterns.items():
            if any(keyword in instruction for keyword in keywords):
                return action_type
        
        return 'analyze'  # 默认为分析
    
    def build_enhanced_context(self, intent: ReferenceIntent, conversation_history: List[Dict], 
                             reference_tracker: Dict) -> Dict[str, Any]:
        """基于引用意图构建增强的上下文"""
        context = {
            'intent': intent,
            'references': {},
            'guidance': []
        }
        
        # 根据引用意图构建具体的引用信息
        for element_type in intent.referenced_elements:
            if element_type in ['analysis', 'chart', 'table']:
                tracker_key = f'last_{element_type}'
                if reference_tracker.get(tracker_key):
                    context['references'][element_type] = reference_tracker[tracker_key]
        
        # 生成智能指导
        context['guidance'] = self._generate_intelligent_guidance(intent, conversation_history)
        
        return context
    
    def _generate_intelligent_guidance(self, intent: ReferenceIntent, conversation_history: List[Dict]) -> List[str]:
        """生成智能指导"""
        guidance = []
        
        if intent.intent_type == 'continuation':
            guidance.append(f"用户希望基于之前的分析结果进行{intent.action_type}，请复用相关变量和数据")
            if conversation_history:
                last_code = conversation_history[-1].get('code', '')
                key_vars = self._extract_key_concepts(last_code)
                if key_vars:
                    guidance.append(f"可以复用的关键变量: {', '.join(key_vars[:3])}")
        
        elif intent.intent_type == 'modification':
            guidance.append("用户希望修改之前的分析，请生成改进版本的代码")
        
        elif intent.intent_type == 'extension':
            guidance.append("用户希望在现有分析基础上增加新的维度或内容")
        
        # 根据引用元素添加具体指导
        if 'analysis' in intent.referenced_elements:
            guidance.append("重点关注之前分析的数据处理逻辑和计算结果")
        
        if 'chart' in intent.referenced_elements:
            guidance.append("考虑图表的展示方式和数据可视化需求")
        
        return guidance

# 使用示例和测试
def test_intelligent_detector():
    """测试智能检测器"""
    print("🧠 智能引用检测器测试")
    print("=" * 60)
    
    detector = IntelligentReferenceDetector()
    
    # 模拟对话历史
    conversation_history = [
        {
            'user_message': '分析各地区销售额',
            'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
            'execution_result': {'success': True}
        }
    ]
    
    # 测试不同的用户指令
    test_cases = [
        "在此基础上，分析各销售员的销售额",
        "基于刚才的结果，看看产品分布",
        "进一步研究地区和产品的关系",
        "修改上面的图表样式",
        "对比不同地区的表现",
        "然后分析销售员的业绩",
        "接下来看看时间趋势",
        "同时统计产品类别"
    ]
    
    for i, instruction in enumerate(test_cases, 1):
        print(f"\n🧪 测试案例 {i}: {instruction}")
        print("-" * 40)
        
        intent = detector.detect_reference_intent(instruction, conversation_history)
        
        if intent:
            print(f"✅ 检测到引用意图:")
            print(f"   类型: {intent.intent_type}")
            print(f"   置信度: {intent.confidence:.2f}")
            print(f"   引用元素: {intent.referenced_elements}")
            print(f"   动作类型: {intent.action_type}")
        else:
            print(f"❌ 未检测到引用意图")

if __name__ == "__main__":
    test_intelligent_detector()
