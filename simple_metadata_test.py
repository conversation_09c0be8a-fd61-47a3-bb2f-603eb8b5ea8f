#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的元数据功能测试
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_metadata_processor():
    """测试元数据处理器"""
    print("🧪 测试元数据处理器...")
    
    try:
        from core.processors.metadata_processor import MetadataProcessor
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '销售额': [8500, 6200, 3200],
            '地区': ['北京', '上海', '广州'],
            '产品名称': ['笔记本电脑', '台式电脑', '平板电脑']
        })
        
        # 创建元数据处理器
        processor = MetadataProcessor()
        
        # 提取元数据
        metadata = processor.extract_dataframe_metadata(test_data, "test_data")
        
        print(f"✅ 元数据提取成功")
        print(f"  列数量: {len(metadata.get('columns', {}))}")
        
        # 测试提示词增强
        context = f"数据形状: {test_data.shape}\n列名: {list(test_data.columns)}"
        instruction = "分析销售数据"
        
        enhanced_prompt = processor.enhance_prompt(instruction, context, metadata, "test_data")
        
        print(f"✅ 提示词增强成功")
        print(f"  原始长度: {len(context + instruction)} 字符")
        print(f"  增强长度: {len(enhanced_prompt)} 字符")
        print(f"  增加了: {len(enhanced_prompt) - len(context + instruction)} 字符")
        
        # 检查关键内容
        if '自动提取的元数据' in enhanced_prompt:
            print("✅ 包含元数据信息")
        else:
            print("❌ 缺少元数据信息")
            
        if '销售额' in enhanced_prompt and 'numeric' in enhanced_prompt:
            print("✅ 包含列类型信息")
        else:
            print("❌ 缺少列类型信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 元数据处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_configuration():
    """测试LLM配置"""
    print("\n🧪 测试LLM配置...")
    
    try:
        from core.llm.llm_factory import LLMFactory
        from core.utils.config import TongyiConfig
        
        # 检查配置
        config = TongyiConfig.from_env()
        print(f"✅ 配置加载成功")
        print(f"  enable_metadata: {config.enable_metadata}")
        
        if config.enable_metadata:
            print("✅ 元数据功能已启用")
        else:
            print("❌ 元数据功能未启用")
        
        return config.enable_metadata
        
    except Exception as e:
        print(f"❌ LLM配置测试失败: {str(e)}")
        return False

def check_environment():
    """检查环境配置"""
    print("\n🧪 检查环境配置...")
    
    import os
    
    metadata_env = os.getenv('ENABLE_METADATA')
    print(f"环境变量 ENABLE_METADATA: {metadata_env}")
    
    if metadata_env and metadata_env.lower() == 'true':
        print("✅ 环境变量配置正确")
        return True
    else:
        print("❌ 环境变量配置错误")
        return False

if __name__ == "__main__":
    print("🔧 简单元数据功能测试...")
    print("=" * 50)
    
    # 测试1: 环境配置
    env_ok = check_environment()
    
    # 测试2: LLM配置
    llm_ok = test_llm_configuration()
    
    # 测试3: 元数据处理器
    processor_ok = test_metadata_processor()
    
    print("=" * 50)
    print("📊 测试结果:")
    print(f"  环境配置: {'✅' if env_ok else '❌'}")
    print(f"  LLM配置: {'✅' if llm_ok else '❌'}")
    print(f"  元数据处理器: {'✅' if processor_ok else '❌'}")
    
    if env_ok and llm_ok and processor_ok:
        print("\n🎉 所有测试通过！元数据功能已成功实现！")
        print("🚀 现在您可以在Streamlit应用中体验增强的数据分析功能！")
    else:
        print("\n⚠️ 部分测试未通过，请检查配置。")
