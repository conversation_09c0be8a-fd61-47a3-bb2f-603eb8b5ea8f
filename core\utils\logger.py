"""
Logging utilities for the data analysis platform.
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime


def get_logger(
    name: str,
    level: int = logging.INFO,
    log_file: Optional[Path] = None,
    enable_console: bool = True
) -> logging.Logger:
    """
    获取配置好的日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径（可选）
        enable_console: 是否启用控制台输出
    
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_llm_logger() -> logging.Logger:
    """获取LLM专用日志记录器"""
    log_dir = Path("logs")
    log_file = log_dir / f"llm_{datetime.now().strftime('%Y%m%d')}.log"
    
    return get_logger(
        name="llm",
        level=logging.INFO,
        log_file=log_file,
        enable_console=False  # LLM日志只写文件，不输出到控制台
    )


def get_app_logger() -> logging.Logger:
    """获取应用程序日志记录器"""
    log_dir = Path("logs")
    log_file = log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
    
    return get_logger(
        name="app",
        level=logging.INFO,
        log_file=log_file,
        enable_console=True
    )
