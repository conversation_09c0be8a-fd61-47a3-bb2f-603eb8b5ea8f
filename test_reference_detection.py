#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试引用检测逻辑
"""

import re

def test_reference_detection():
    """测试引用检测"""
    print("🧪 测试引用检测逻辑")
    print("=" * 50)
    
    instruction = "在此基础上，分析销售人员销售额"
    instruction_lower = instruction.lower()
    
    print(f"测试指令: {instruction}")
    print(f"小写转换: {instruction_lower}")
    
    # 语义模式匹配
    reference_patterns = {
        'continuation': [
            r'(在.*基础上|基于.*|根据.*|参考.*)',
            r'(进一步.*|深入.*|详细.*|具体.*)',
            r'(接着.*|然后.*|继续.*)'
        ],
        'modification': [
            r'(修改.*|调整.*|改变.*|优化.*)',
            r'(重新.*|再次.*|重做.*)',
            r'(换成.*|改为.*|变成.*)'
        ],
        'comparison': [
            r'(对比.*|比较.*|对照.*)',
            r'(差异.*|区别.*|不同.*)',
            r'(相比.*|与.*比较)'
        ],
        'extension': [
            r'(同时.*|另外.*|此外.*|还要.*)',
            r'(加上.*|增加.*|补充.*)',
            r'(以及.*|和.*一起)'
        ]
    }
    
    best_type = 'independent'
    max_confidence = 0.0
    
    print("\n🔍 模式匹配结果:")
    
    for ref_type, patterns in reference_patterns.items():
        pattern_matches = 0
        matched_patterns = []
        
        for pattern in patterns:
            if re.search(pattern, instruction_lower):
                pattern_matches += 1
                matched_patterns.append(pattern)
        
        print(f"\n{ref_type}:")
        print(f"  匹配数量: {pattern_matches}/{len(patterns)}")
        print(f"  匹配模式: {matched_patterns}")
        
        if pattern_matches > 0:
            # 修复后的置信度计算
            base_confidence = 0.7  # 基础置信度
            pattern_bonus = 0.2 * (pattern_matches / len(patterns))  # 模式匹配奖励

            confidence = base_confidence + pattern_bonus

            # 模拟有历史对话
            recent_rounds = [{'code': 'test'}]  # 模拟有历史
            if recent_rounds:
                confidence += 0.1  # 上下文奖励分
            
            print(f"  置信度: {confidence:.2f}")
            
            if confidence > max_confidence:
                max_confidence = confidence
                best_type = ref_type
    
    print(f"\n📊 最终结果:")
    print(f"  最佳类型: {best_type}")
    print(f"  最高置信度: {max_confidence:.2f}")
    print(f"  是否有引用: {max_confidence >= 0.5}")
    
    # 测试具体的模式
    print(f"\n🔍 具体模式测试:")
    test_pattern = r'(在.*基础上|基于.*|根据.*|参考.*)'
    match = re.search(test_pattern, instruction_lower)
    print(f"  模式: {test_pattern}")
    print(f"  匹配结果: {match}")
    if match:
        print(f"  匹配内容: {match.group()}")

if __name__ == "__main__":
    test_reference_detection()
