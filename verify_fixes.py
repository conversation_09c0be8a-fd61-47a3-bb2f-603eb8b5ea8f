import sys
from pathlib import Path

def main():
    print("Testing Streamlit fixes...")
    
    main_file = Path("app/main.py")
    if not main_file.exists():
        print("ERROR: main.py not found")
        return False
    
    try:
        content = main_file.read_text(encoding='utf-8')
        
        # Test 1: Chat message initialization
        if "if not st.session_state.chat_messages:" in content:
            print("PASS: Chat message initialization")
        else:
            print("FAIL: Chat message initialization")
            return False
        
        # Test 2: Sidebar usage
        if "with st.sidebar:" in content:
            print("PASS: Sidebar usage")
        else:
            print("FAIL: Sidebar usage")
            return False
        
        # Test 3: Removed complex HTML
        if "status-float" not in content:
            print("PASS: Removed complex HTML")
        else:
            print("FAIL: Still has complex HTML")
            return False
        
        print("All tests passed!")
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
