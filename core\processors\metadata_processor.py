"""
元数据处理器 - 负责处理数据元数据，增强LLM提示词
"""

from typing import Dict, Any, List, Optional
import pandas as pd
from ..utils.logger import get_app_logger
from ..metadata.metadata_manager import metadata_manager


class MetadataProcessor:
    """
    元数据处理器
    
    负责处理数据元数据，为LLM提供更丰富的上下文信息。
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化元数据处理器
        
        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
    
    def enhance_prompt(self, instruction: str, context: str, metadata: Optional[Dict] = None, table_name: str = "data") -> str:
        """
        使用元数据增强提示词

        Args:
            instruction: 原始用户指令
            context: 数据上下文
            metadata: 元数据字典（自动提取的）
            table_name: 表格名称，用于获取业务元数据

        Returns:
            增强后的提示词
        """
        # 尝试获取业务元数据
        business_metadata = metadata_manager.get_table_metadata(table_name)

        if not metadata and not business_metadata:
            return self._build_basic_prompt(instruction, context)

        if self.logger:
            self.logger.info(f"使用元数据增强提示词 - 表格: {table_name}")

        # 构建增强的提示词
        enhanced_prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{context}

"""

        # 添加自动提取的元数据
        if metadata:
            enhanced_prompt += f"""自动提取的元数据:
{self._format_metadata(metadata)}

"""

        # 添加业务元数据
        if business_metadata:
            business_context = metadata_manager.generate_llm_context(table_name)
            enhanced_prompt += f"""业务元数据:
{business_context}

"""

        enhanced_prompt += f"""用户指令: {instruction}

要求:
1. 只返回可执行的Python代码，不要解释
2. 数据已经加载在变量 df 中，不要重新读取文件
3. 使用pandas进行数据处理，直接使用 df 变量
4. 充分利用元数据信息理解数据含义和业务语义
5. 根据列的业务含义选择合适的分析方法
6. 代码要简洁高效，确保语法正确
7. 使用Streamlit组件显示结果（st.write, st.dataframe, st.bar_chart等）

请生成代码:"""

        return enhanced_prompt

    def format_metadata_for_prompt(self, metadata: Dict) -> str:
        """
        将元数据格式化为适合LLM提示词的文本

        Args:
            metadata: 元数据字典

        Returns:
            格式化后的元数据文本
        """
        if not metadata:
            return ""

        formatted_parts = []

        # 添加列信息
        if 'columns' in metadata:
            formatted_parts.append("📊 列信息分析:")
            for col_name, col_info in metadata['columns'].items():
                col_desc = [f"  • {col_name}:"]

                # 数据类型
                if 'dtype' in col_info:
                    col_desc.append(f"类型={col_info['dtype']}")

                # 业务含义（如果有）
                if 'business_meaning' in col_info:
                    col_desc.append(f"含义={col_info['business_meaning']}")

                # 取值范围
                if 'unique_values' in col_info and len(col_info['unique_values']) <= 10:
                    col_desc.append(f"取值={col_info['unique_values']}")
                elif 'value_range' in col_info:
                    col_desc.append(f"范围={col_info['value_range']}")

                # 推荐分析方法
                if 'recommended_analysis' in col_info:
                    col_desc.append(f"推荐分析={col_info['recommended_analysis']}")

                formatted_parts.append(" ".join(col_desc))

        # 添加表级元数据
        if 'table_info' in metadata:
            table_info = metadata['table_info']
            formatted_parts.append("\n📋 表信息:")

            if 'business_description' in table_info:
                formatted_parts.append(f"  • 业务描述: {table_info['business_description']}")

            if 'key_relationships' in table_info:
                formatted_parts.append(f"  • 关键关系: {table_info['key_relationships']}")

            if 'recommended_charts' in table_info:
                formatted_parts.append(f"  • 推荐图表: {', '.join(table_info['recommended_charts'])}")

        return "\n".join(formatted_parts)

    def _build_basic_prompt(self, instruction: str, context: str) -> str:
        """构建基础提示词（无元数据）"""
        return f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{context}

用户指令: {instruction}

要求:
1. 只返回可执行的Python代码，不要解释
2. 数据已经加载在变量 df 中，不要重新读取文件
3. 使用pandas进行数据处理，直接使用 df 变量
4. 代码要简洁高效，确保语法正确
5. 使用Streamlit组件显示结果（st.write, st.dataframe, st.bar_chart等）

请生成代码:"""
    
    def _format_metadata(self, metadata: Dict) -> str:
        """格式化元数据为可读文本"""
        formatted_parts = []
        
        # 表级元数据
        if 'table_description' in metadata:
            formatted_parts.append(f"表描述: {metadata['table_description']}")
        
        if 'business_context' in metadata:
            formatted_parts.append(f"业务背景: {metadata['business_context']}")
        
        # 列级元数据
        if 'columns' in metadata:
            formatted_parts.append("\n列信息:")
            for col_name, col_info in metadata['columns'].items():
                col_desc = []
                if 'description' in col_info:
                    col_desc.append(f"描述: {col_info['description']}")
                if 'data_type' in col_info:
                    col_desc.append(f"类型: {col_info['data_type']}")
                if 'unit' in col_info:
                    col_desc.append(f"单位: {col_info['unit']}")
                if 'constraints' in col_info:
                    col_desc.append(f"约束: {col_info['constraints']}")
                
                if col_desc:
                    formatted_parts.append(f"  - {col_name}: {', '.join(col_desc)}")
        
        # 关系信息
        if 'relationships' in metadata:
            formatted_parts.append("\n数据关系:")
            for rel in metadata['relationships']:
                formatted_parts.append(f"  - {rel}")
        
        return '\n'.join(formatted_parts)
    
    def extract_dataframe_metadata(self, df: pd.DataFrame, table_name: str = "data") -> Dict[str, Any]:
        """
        从DataFrame提取基础元数据
        
        Args:
            df: 要分析的DataFrame
            table_name: 表名
            
        Returns:
            提取的元数据字典
        """
        if self.logger:
            self.logger.info(f"提取DataFrame元数据: {table_name}")
        
        metadata = {
            'table_name': table_name,
            'shape': df.shape,
            'columns': {},
            'summary': {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'memory_usage': df.memory_usage(deep=True).sum(),
                'missing_values': df.isnull().sum().sum(),
            }
        }
        
        # 分析每一列
        for col in df.columns:
            col_metadata = self._analyze_column(df[col], col)

            # 添加业务语义推断
            semantics = self.infer_column_semantics(col, df[col])
            col_metadata.update(semantics)

            metadata['columns'][col] = col_metadata

        # 添加表级推荐
        metadata['table_info'] = self._infer_table_recommendations(df, metadata['columns'])

        return metadata
    
    def _analyze_column(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """分析单个列的元数据"""
        col_info = {
            'name': col_name,
            'dtype': str(series.dtype),
            'non_null_count': series.count(),
            'null_count': series.isnull().sum(),
            'unique_count': series.nunique(),
        }
        
        # 数值型列的统计信息
        if pd.api.types.is_numeric_dtype(series):
            col_info.update({
                'data_type': 'numeric',
                'min': series.min(),
                'max': series.max(),
                'mean': series.mean(),
                'std': series.std(),
                'median': series.median(),
            })
            
            # 检测可能的类别
            if series.nunique() <= 10:
                col_info['possible_categories'] = series.value_counts().to_dict()
        
        # 文本型列的信息
        elif pd.api.types.is_string_dtype(series) or series.dtype == 'object':
            col_info.update({
                'data_type': 'text',
                'avg_length': series.astype(str).str.len().mean(),
                'max_length': series.astype(str).str.len().max(),
            })
            
            # 如果唯一值较少，可能是类别
            if series.nunique() <= 20:
                col_info['possible_categories'] = series.value_counts().head(10).to_dict()
        
        # 日期时间型列
        elif pd.api.types.is_datetime64_any_dtype(series):
            col_info.update({
                'data_type': 'datetime',
                'min_date': series.min(),
                'max_date': series.max(),
                'date_range': (series.max() - series.min()).days,
            })
        
        return col_info
    
    def infer_column_semantics(self, col_name: str, col_data: pd.Series) -> Dict[str, str]:
        """
        推断列的语义信息
        
        Args:
            col_name: 列名
            col_data: 列数据
            
        Returns:
            推断的语义信息
        """
        semantics = {}
        col_name_lower = col_name.lower()
        
        # 基于列名推断
        if any(keyword in col_name_lower for keyword in ['id', 'key', '编号', '序号']):
            semantics['semantic_type'] = 'identifier'
        elif any(keyword in col_name_lower for keyword in ['name', 'title', '名称', '标题']):
            semantics['semantic_type'] = 'name'
        elif any(keyword in col_name_lower for keyword in ['date', 'time', '日期', '时间']):
            semantics['semantic_type'] = 'temporal'
        elif any(keyword in col_name_lower for keyword in ['amount', 'price', 'cost', '金额', '价格', '费用']):
            semantics['semantic_type'] = 'monetary'
        elif any(keyword in col_name_lower for keyword in ['count', 'num', 'quantity', '数量', '个数']):
            semantics['semantic_type'] = 'quantity'
        elif any(keyword in col_name_lower for keyword in ['rate', 'ratio', 'percent', '比率', '百分比']):
            semantics['semantic_type'] = 'ratio'
        
        # 基于数据特征推断
        if pd.api.types.is_numeric_dtype(col_data):
            if col_data.nunique() <= 10 and col_data.min() >= 0:
                semantics['possible_role'] = 'categorical_numeric'
                semantics['recommended_analysis'] = '分组统计、饼图、柱状图'
            elif col_data.between(0, 1).all():
                semantics['possible_role'] = 'probability_or_ratio'
                semantics['recommended_analysis'] = '分布分析、直方图'
            elif col_data.min() >= 0 and col_data.max() <= 100:
                semantics['possible_role'] = 'percentage'
                semantics['recommended_analysis'] = '百分比分析、饼图'
            else:
                semantics['recommended_analysis'] = '描述性统计、箱线图、散点图'
        elif pd.api.types.is_string_dtype(col_data) or col_data.dtype == 'object':
            if col_data.nunique() <= 20:
                semantics['recommended_analysis'] = '频次统计、柱状图、饼图'
            else:
                semantics['recommended_analysis'] = '文本分析、词频统计'
        elif pd.api.types.is_datetime64_any_dtype(col_data):
            semantics['recommended_analysis'] = '时间序列分析、趋势图'

        # 添加业务含义推断
        if semantics.get('semantic_type') == 'monetary':
            semantics['business_meaning'] = '金额/价格相关指标，适合求和、平均值分析'
        elif semantics.get('semantic_type') == 'quantity':
            semantics['business_meaning'] = '数量指标，适合计数、求和分析'
        elif semantics.get('semantic_type') == 'temporal':
            semantics['business_meaning'] = '时间维度，适合时间序列分析'
        elif semantics.get('semantic_type') == 'name':
            semantics['business_meaning'] = '名称/标识，适合分组分析'
        elif semantics.get('semantic_type') == 'identifier':
            semantics['business_meaning'] = '唯一标识，通常用于关联分析'

        # 添加取值信息
        if col_data.nunique() <= 10:
            semantics['unique_values'] = col_data.value_counts().head(10).to_dict()
        elif pd.api.types.is_numeric_dtype(col_data):
            semantics['value_range'] = f"{col_data.min():.2f} - {col_data.max():.2f}"

        return semantics

    def _infer_table_recommendations(self, df: pd.DataFrame, columns_metadata: Dict) -> Dict[str, Any]:
        """
        推断表级分析建议

        Args:
            df: DataFrame
            columns_metadata: 列元数据

        Returns:
            表级推荐信息
        """
        recommendations = {
            'business_description': '',
            'key_relationships': [],
            'recommended_charts': [],
            'analysis_suggestions': []
        }

        # 统计不同类型的列
        numeric_cols = []
        categorical_cols = []
        datetime_cols = []
        id_cols = []

        for col_name, col_info in columns_metadata.items():
            if col_info.get('data_type') == 'numeric':
                numeric_cols.append(col_name)
            elif col_info.get('data_type') == 'text' and col_info.get('unique_count', 0) <= 20:
                categorical_cols.append(col_name)
            elif col_info.get('data_type') == 'datetime':
                datetime_cols.append(col_name)
            elif col_info.get('semantic_type') == 'identifier':
                id_cols.append(col_name)

        # 推荐图表类型
        if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
            recommendations['recommended_charts'].extend(['bar_chart', 'pie_chart'])
            recommendations['analysis_suggestions'].append('按类别分组分析数值指标')

        if len(numeric_cols) >= 2:
            recommendations['recommended_charts'].extend(['scatter_plot', 'correlation_heatmap'])
            recommendations['analysis_suggestions'].append('数值变量间的相关性分析')

        if len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
            recommendations['recommended_charts'].extend(['line_chart', 'time_series'])
            recommendations['analysis_suggestions'].append('时间序列趋势分析')

        if len(categorical_cols) >= 1:
            recommendations['recommended_charts'].append('distribution_chart')
            recommendations['analysis_suggestions'].append('类别分布分析')

        # 推断业务场景
        col_names_lower = [col.lower() for col in df.columns]
        if any('sales' in name or '销售' in name for name in col_names_lower):
            recommendations['business_description'] = '销售数据分析表'
        elif any('user' in name or '用户' in name for name in col_names_lower):
            recommendations['business_description'] = '用户行为数据表'
        elif any('product' in name or '产品' in name for name in col_names_lower):
            recommendations['business_description'] = '产品相关数据表'
        else:
            recommendations['business_description'] = '业务数据分析表'

        # 关键关系推断
        if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
            recommendations['key_relationships'].append(f"类别变量({', '.join(categorical_cols[:2])})与数值变量({', '.join(numeric_cols[:2])})的关系")

        if len(datetime_cols) >= 1:
            recommendations['key_relationships'].append(f"时间维度({', '.join(datetime_cols)})的变化趋势")

        return recommendations

    def suggest_analysis_types(self, metadata: Dict) -> List[str]:
        """
        基于元数据建议分析类型
        
        Args:
            metadata: 数据元数据
            
        Returns:
            建议的分析类型列表
        """
        suggestions = []
        
        # 基于列类型建议
        numeric_cols = []
        categorical_cols = []
        datetime_cols = []
        
        for col_name, col_info in metadata.get('columns', {}).items():
            if col_info.get('data_type') == 'numeric':
                numeric_cols.append(col_name)
            elif col_info.get('data_type') == 'text' and col_info.get('unique_count', 0) <= 20:
                categorical_cols.append(col_name)
            elif col_info.get('data_type') == 'datetime':
                datetime_cols.append(col_name)
        
        # 建议分析类型
        if len(numeric_cols) >= 2:
            suggestions.append('correlation_analysis')
            suggestions.append('scatter_plot')
        
        if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
            suggestions.append('group_analysis')
            suggestions.append('bar_chart')
        
        if len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
            suggestions.append('time_series_analysis')
            suggestions.append('line_chart')
        
        if len(numeric_cols) >= 1:
            suggestions.append('descriptive_statistics')
            suggestions.append('distribution_analysis')
        
        return suggestions
