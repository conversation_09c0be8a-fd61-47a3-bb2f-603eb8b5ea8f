# PandasAI V2 安装和配置指南

## 📋 概述

本指南帮助您完全卸载PandasAI V3并安装配置PandasAI V2稳定版。

## ✅ 完成状态

- [x] 完全卸载PandasAI V3及其扩展包
- [x] 清理pip缓存
- [x] 安装PandasAI V2 (2.3.2)
- [x] 验证安装成功
- [x] 创建配置示例和测试文件

## 🔧 已执行的操作

### 1. 卸载PandasAI V3
```bash
pip uninstall pandasai pandasai-litellm pandasai-openai -y
pip cache purge
```

### 2. 安装PandasAI V2
```bash
pip install "pandasai<3.0.0" --upgrade
```

### 3. 验证安装
- PandasAI版本: 2.3.2
- Pandas版本: 1.5.3 (V2要求的版本)
- 所有核心组件导入成功

## 📁 创建的文件

1. **final_verification.py** - 安装验证脚本 ✅
2. **pandasai_v2_examples.py** - 详细使用示例
3. **test_pandasai_v2.py** - 交互式测试脚本
4. **working_example.py** - 工作示例
5. **.env.example** - 环境变量配置示例

## 🚀 快速开始

### 基本使用
```python
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# 1. 配置LLM
llm = OpenAI(api_token="your-openai-api-key")

# 2. 创建数据
df = pd.DataFrame({
    'name': ['Alice', 'Bob', 'Charlie'],
    'salary': [50000, 60000, 70000]
})

# 3. 创建SmartDataframe
smart_df = SmartDataframe(df, config={"llm": llm})

# 4. 自然语言查询
result = smart_df.chat("What is the average salary?")
print(result)
```

### 通义千问配置
```python
from pandasai.llm import OpenAI

# 使用OpenAI兼容接口
llm = OpenAI(
    api_token="your-dashscope-api-key",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus",
    temperature=0.1
)

smart_df = SmartDataframe(df, config={"llm": llm})
result = smart_df.chat("分析数据趋势")
```

## 🔑 API密钥配置

### 环境变量方式
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-key"

# 通义千问
export DASHSCOPE_API_KEY="your-dashscope-key"
```

### 代码中直接配置
```python
# OpenAI
llm = OpenAI(api_token="sk-...")

# 通义千问
llm = OpenAI(
    api_token="sk-...",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus"
)
```

## 📊 V2 vs V3 主要差异

| 特性 | PandasAI V2 (当前) | PandasAI V3 (Beta) |
|------|-------------------|-------------------|
| 核心类 | `SmartDataframe` | `pai.DataFrame` |
| LLM配置 | `config={"llm": llm}` | `pai.config.set({"llm": llm})` |
| 查询方式 | `smart_df.chat("query")` | `df.chat("query")` |
| 扩展包 | 内置支持 | 需要安装扩展包 |
| 稳定性 | 稳定版，生产可用 | Beta版本 |
| 架构 | 简单直观 | 语义层+自然语言层 |

## 🎯 推荐使用V2的原因

- ✅ **稳定可靠**: 生产环境验证
- ✅ **文档完整**: 社区支持好
- ✅ **API简单**: 学习成本低
- ✅ **兼容性好**: 依赖关系稳定
- ✅ **功能完整**: 满足大部分需求

## 🔧 配置选项

```python
config = {
    "llm": llm,                    # LLM实例
    "verbose": True,               # 显示详细信息
    "conversational": False,       # 对话模式
    "save_charts": True,           # 保存图表
    "save_charts_path": "./charts/", # 图表路径
    "enable_cache": True,          # 启用缓存
    "max_retries": 3,              # 最大重试次数
}

smart_df = SmartDataframe(df, config=config)
```

## 🐛 常见问题

### 1. 导入错误
确保使用正确的导入方式：
```python
from pandasai import SmartDataframe  # ✅ 正确
from pandasai import DataFrame       # ❌ 这是V3的方式
```

### 2. API密钥错误
确保设置了正确的环境变量或在代码中指定API密钥。

### 3. 依赖冲突
如果遇到依赖冲突，可以创建新的虚拟环境：
```bash
python -m venv pandasai_v2_env
source pandasai_v2_env/bin/activate  # Linux/Mac
# 或
pandasai_v2_env\Scripts\activate     # Windows
pip install "pandasai<3.0.0"
```

## 📚 更多资源

- [PandasAI V2 官方文档](https://docs.pandas-ai.com/)
- [OpenAI API文档](https://platform.openai.com/docs)
- [通义千问API文档](https://help.aliyun.com/document_detail/2712576.html)

## 🎉 总结

PandasAI V2已成功安装并配置完成！您现在可以：

1. 使用自然语言查询数据
2. 支持多种LLM (OpenAI、通义千问等)
3. 生成图表和可视化
4. 进行复杂的数据分析

开始您的数据分析之旅吧！🚀
