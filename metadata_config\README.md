# 元数据配置目录

这个目录包含了PandasAI应用的表格元数据配置文件，用于帮助大语言模型更好地理解数据结构和业务含义。

## 文件结构

```
metadata_config/
├── README.md                    # 说明文档
├── tables_metadata.json        # 表格元数据配置
├── column_templates.json       # 列模板配置
├── examples/                   # 配置示例
│   ├── sales_data_example.json
│   └── template_example.yaml
└── backups/                    # 配置备份
    └── YYYY-MM-DD/
```

## 配置文件说明

### 1. tables_metadata.json
存储所有已注册表格的元数据信息，包括：
- 表格基本信息（名称、描述、业务领域）
- 列详细信息（名称、描述、业务含义、数据类型、示例值等）
- 列间关系和约束条件
- 版本信息和时间戳

### 2. column_templates.json
存储列模板配置，用于自动推断新列的元数据：
- 按业务领域分类的列模板
- 每个模板包含描述、业务含义、数据类型、约束条件等
- 支持关键词匹配和智能推荐

## 使用方式

### 自动模式
当上传新数据文件时，系统会：
1. 自动分析列名和数据类型
2. 匹配最相似的列模板
3. 生成初始元数据配置
4. 保存到配置文件中

### 手动模式
运营团队可以：
1. 直接编辑JSON配置文件
2. 使用Streamlit界面进行可视化编辑
3. 导入/导出配置文件
4. 批量更新元数据

## 配置示例

### 表格元数据示例
```json
{
  "sales_data": {
    "table_name": "sales_data",
    "description": "销售数据表，包含产品销售的详细记录",
    "business_domain": "销售管理",
    "columns": {
      "产品名称": {
        "name": "产品名称",
        "display_name": "产品名称",
        "description": "销售产品的具体名称或型号",
        "data_type": "object",
        "business_meaning": "用于产品分析和库存管理的标识",
        "examples": ["笔记本电脑", "台式电脑", "平板电脑"],
        "constraints": {},
        "tags": ["产品", "标识", "分类"]
      }
    },
    "relationships": {
      "销售额_销量": "销售额 = 单价 × 销量"
    },
    "primary_keys": ["日期", "产品名称"],
    "version": "1.0.0"
  }
}
```

### 列模板示例
```json
{
  "销售相关": {
    "销售额": {
      "description": "产品或服务的销售金额",
      "business_meaning": "反映业务收入情况的核心指标",
      "data_type": "float",
      "constraints": {"min": 0},
      "tags": ["财务", "收入", "KPI"],
      "keywords": ["销售额", "收入", "营收", "金额"]
    }
  }
}
```

## 最佳实践

### 1. 命名规范
- 表格名称：使用下划线分隔的小写英文，如 `sales_data`
- 列名：保持原始中文名称，便于业务理解
- 模板名称：使用标准的业务术语

### 2. 描述规范
- 表格描述：简洁明了地说明表格用途和数据来源
- 列描述：详细说明列的含义和计算方式
- 业务含义：从业务角度解释数据的价值和用途

### 3. 标签规范
- 使用统一的标签体系，如：财务、销售、产品、地理、时间等
- 每列建议2-4个标签
- 标签有助于数据发现和关联分析

### 4. 维护规范
- 定期备份配置文件
- 版本控制重要变更
- 文档化配置变更原因
- 定期验证元数据的准确性

## 故障排除

### 常见问题
1. **配置文件损坏**：从backups目录恢复最近的备份
2. **模板匹配不准确**：调整关键词或添加新模板
3. **元数据缺失**：使用自动推断功能重新生成
4. **性能问题**：定期清理过期的元数据

### 日志位置
- 应用日志：查看控制台输出
- 错误日志：检查Python异常信息
- 配置变更：记录在元数据的updated_at字段

## 联系方式

如有问题或建议，请联系：
- 技术支持：查看应用文档
- 业务咨询：联系数据管理团队
