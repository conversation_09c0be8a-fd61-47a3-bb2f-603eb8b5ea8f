#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文日志查看工具
实时查看连续追问功能的详细日志
"""

import os
import sys
import time
import glob
from pathlib import Path
from datetime import datetime
import argparse


def find_latest_log_file():
    """查找最新的日志文件"""
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return None
    
    # 查找所有日志文件
    log_files = list(log_dir.glob("app_*.log"))
    if not log_files:
        print("❌ 未找到日志文件")
        return None
    
    # 返回最新的日志文件
    latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
    return latest_log


def filter_context_logs(log_content):
    """过滤出上下文相关的日志"""
    lines = log_content.split('\n')
    context_lines = []
    
    # 关键词过滤
    context_keywords = [
        "📝 生成的对话摘要详情:",
        "🧠 为LLM构建的上下文信息:",
        "🤖 发送给大模型的完整提示词:",
        "💬 添加新的对话轮次:",
        "📊 对话轮次添加完成",
        "🔄 开始生成对话摘要",
        "✅ 对话摘要生成成功",
        "⚠️ 对话摘要生成失败",
        "=" * 80,
        "=" * 100,
        "-" * 100
    ]
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # 检查是否包含关键词
        if any(keyword in line for keyword in context_keywords):
            context_lines.append(line)
            
            # 如果是分隔符开始，读取整个块
            if "=" * 80 in line or "=" * 100 in line:
                i += 1
                # 读取到下一个分隔符或文件结束
                while i < len(lines):
                    context_lines.append(lines[i])
                    if ("=" * 80 in lines[i] or "=" * 100 in lines[i]) and lines[i] != line:
                        break
                    i += 1
        i += 1
    
    return '\n'.join(context_lines)


def tail_log_file(log_file, follow=False, context_only=False):
    """读取日志文件内容"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            if follow:
                # 跟踪模式 - 实时显示新内容
                f.seek(0, 2)  # 移动到文件末尾
                print(f"🔍 实时监控日志文件: {log_file}")
                print("按 Ctrl+C 退出")
                print("-" * 80)
                
                while True:
                    line = f.readline()
                    if line:
                        if context_only:
                            # 检查是否是上下文相关日志
                            context_keywords = [
                                "📝", "🧠", "🤖", "💬", "📊", "🔄", "✅", "⚠️",
                                "生成的对话摘要", "构建的上下文", "发送给大模型", 
                                "添加新的对话轮次", "对话摘要生成"
                            ]
                            if any(keyword in line for keyword in context_keywords):
                                print(line.rstrip())
                        else:
                            print(line.rstrip())
                    else:
                        time.sleep(0.1)
            else:
                # 一次性读取模式
                content = f.read()
                if context_only:
                    content = filter_context_logs(content)
                print(content)
                
    except FileNotFoundError:
        print(f"❌ 日志文件不存在: {log_file}")
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")


def show_log_summary():
    """显示日志摘要信息"""
    log_file = find_latest_log_file()
    if not log_file:
        return
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计关键信息
        summary_count = content.count("📝 生成的对话摘要详情:")
        context_count = content.count("🧠 为LLM构建的上下文信息:")
        prompt_count = content.count("🤖 发送给大模型的完整提示词:")
        round_count = content.count("💬 添加新的对话轮次:")
        
        print("📊 日志统计摘要:")
        print(f"  日志文件: {log_file}")
        print(f"  文件大小: {log_file.stat().st_size / 1024:.1f} KB")
        print(f"  最后修改: {datetime.fromtimestamp(log_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  生成摘要次数: {summary_count}")
        print(f"  构建上下文次数: {context_count}")
        print(f"  发送提示词次数: {prompt_count}")
        print(f"  添加对话轮次: {round_count}")
        
    except Exception as e:
        print(f"❌ 分析日志文件失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="上下文日志查看工具")
    parser.add_argument('-f', '--follow', action='store_true', help='实时跟踪日志')
    parser.add_argument('-c', '--context-only', action='store_true', help='只显示上下文相关日志')
    parser.add_argument('-s', '--summary', action='store_true', help='显示日志统计摘要')
    parser.add_argument('--file', type=str, help='指定日志文件路径')
    
    args = parser.parse_args()
    
    print("🔍 上下文日志查看工具")
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    if args.summary:
        show_log_summary()
        return
    
    # 确定日志文件
    if args.file:
        log_file = Path(args.file)
        if not log_file.exists():
            print(f"❌ 指定的日志文件不存在: {log_file}")
            return
    else:
        log_file = find_latest_log_file()
        if not log_file:
            return
    
    print(f"📄 使用日志文件: {log_file}")
    
    if args.context_only:
        print("🎯 只显示上下文相关日志")
    
    if args.follow:
        print("👀 实时跟踪模式")
    
    print("-" * 60)
    
    # 读取日志
    tail_log_file(log_file, follow=args.follow, context_only=args.context_only)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        sys.exit(1)
