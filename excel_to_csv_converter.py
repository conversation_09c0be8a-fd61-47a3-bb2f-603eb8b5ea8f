#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel to CSV Converter
将Excel文件转换为CSV文件的工具

支持功能：
- 支持.xlsx和.xls格式
- 可选择特定工作表或转换所有工作表
- 自定义输出路径和文件名
- 处理中文编码
- 错误处理和日志记录
"""

import pandas as pd
import os
import sys
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_to_csv.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def excel_to_csv(excel_file_path, output_dir=None, sheet_name=None, encoding='utf-8-sig'):
    """
    将Excel文件转换为CSV文件
    
    参数:
        excel_file_path (str): Excel文件路径
        output_dir (str, optional): 输出目录，默认为Excel文件所在目录
        sheet_name (str/int/list, optional): 工作表名称、索引或列表，None表示所有工作表
        encoding (str): CSV文件编码，默认utf-8-sig（支持中文且Excel兼容）
    
    返回:
        list: 成功转换的CSV文件路径列表
    """
    try:
        # 验证输入文件
        excel_path = Path(excel_file_path)
        if not excel_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {excel_file_path}")
        
        if excel_path.suffix.lower() not in ['.xlsx', '.xls']:
            raise ValueError(f"不支持的文件格式: {excel_path.suffix}")
        
        # 设置输出目录
        if output_dir is None:
            output_dir = excel_path.parent
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始转换Excel文件: {excel_file_path}")
        
        # 读取Excel文件
        if sheet_name is None:
            # 读取所有工作表
            excel_data = pd.read_excel(excel_file_path, sheet_name=None, engine='openpyxl' if excel_path.suffix.lower() == '.xlsx' else 'xlrd')
            sheets_to_convert = excel_data
        else:
            # 读取指定工作表
            excel_data = pd.read_excel(excel_file_path, sheet_name=sheet_name, engine='openpyxl' if excel_path.suffix.lower() == '.xlsx' else 'xlrd')
            if isinstance(sheet_name, (list, tuple)):
                sheets_to_convert = excel_data
            else:
                sheets_to_convert = {sheet_name if isinstance(sheet_name, str) else f"Sheet_{sheet_name}": excel_data}
        
        converted_files = []
        
        # 转换每个工作表
        for sheet_name, df in sheets_to_convert.items():
            # 生成CSV文件名
            base_name = excel_path.stem
            if len(sheets_to_convert) > 1:
                csv_filename = f"{base_name}_{sheet_name}.csv"
            else:
                csv_filename = f"{base_name}.csv"
            
            csv_path = output_dir / csv_filename
            
            # 保存为CSV
            df.to_csv(csv_path, index=False, encoding=encoding)
            converted_files.append(str(csv_path))
            
            logger.info(f"成功转换工作表 '{sheet_name}' -> {csv_path}")
            logger.info(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
        
        logger.info(f"转换完成！共转换 {len(converted_files)} 个文件")
        return converted_files
        
    except Exception as e:
        logger.error(f"转换失败: {str(e)}")
        raise


def batch_convert(input_dir, output_dir=None, file_pattern="*.xlsx"):
    """
    批量转换目录中的Excel文件
    
    参数:
        input_dir (str): 输入目录
        output_dir (str, optional): 输出目录
        file_pattern (str): 文件匹配模式
    
    返回:
        dict: 转换结果统计
    """
    input_path = Path(input_dir)
    if not input_path.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")
    
    # 查找Excel文件
    excel_files = list(input_path.glob(file_pattern))
    if file_pattern == "*.xlsx":
        excel_files.extend(input_path.glob("*.xls"))
    
    if not excel_files:
        logger.warning(f"在目录 {input_dir} 中未找到Excel文件")
        return {"success": 0, "failed": 0, "files": []}
    
    results = {"success": 0, "failed": 0, "files": []}
    
    for excel_file in excel_files:
        try:
            converted = excel_to_csv(str(excel_file), output_dir)
            results["success"] += 1
            results["files"].extend(converted)
        except Exception as e:
            logger.error(f"转换文件 {excel_file} 失败: {str(e)}")
            results["failed"] += 1
    
    return results


def main():
    """
    主函数 - 命令行使用示例
    """
    # 使用示例
    print("Excel to CSV 转换工具")
    print("=" * 50)
    
    # 示例1: 转换单个文件
    try:
        # 请修改为您的Excel文件路径
        excel_file = "sales_data.xlsx"  # 替换为实际文件路径
        
        if os.path.exists(excel_file):
            print(f"\n正在转换文件: {excel_file}")
            converted_files = excel_to_csv(excel_file)
            print(f"转换成功！生成的CSV文件:")
            for file in converted_files:
                print(f"  - {file}")
        else:
            print(f"示例文件 {excel_file} 不存在")
            print("\n使用方法:")
            print("1. 将要转换的Excel文件放在当前目录")
            print("2. 修改代码中的文件名")
            print("3. 运行程序")
            
            # 交互式输入
            user_file = input("\n请输入Excel文件路径 (或按Enter跳过): ").strip()
            if user_file and os.path.exists(user_file):
                converted_files = excel_to_csv(user_file)
                print(f"转换成功！生成的CSV文件:")
                for file in converted_files:
                    print(f"  - {file}")
    
    except Exception as e:
        print(f"转换失败: {str(e)}")


if __name__ == "__main__":
    main()
