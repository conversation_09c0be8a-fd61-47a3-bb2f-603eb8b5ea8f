# Streamlit配置文件
# 参考: https://docs.streamlit.io/library/advanced-features/configuration

[global]
# 开发模式配置
developmentMode = false
showWarningOnDirectExecution = false

[logger]
# 日志级别: "error", "warning", "info", "debug"
level = "info"
messageFormat = "%(asctime)s %(message)s"

[client]
# 工具栏模式: "auto", "developer", "viewer", "minimal"
toolbarMode = "minimal"
showErrorDetails = true
caching = true

[server]
# 服务器配置
port = 8501
address = "localhost"
headless = false
runOnSave = false
allowRunOnSave = false
enableCORS = false
enableXsrfProtection = true
maxUploadSize = 200
maxMessageSize = 200
enableWebsocketCompression = false

[browser]
# 浏览器配置
gatherUsageStats = false
serverAddress = "localhost"
serverPort = 8501

[theme]
# 主题配置
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
font = "sans serif"

[runner]
# 运行器配置
magicEnabled = true
installTracer = false
fixMatplotlib = true
postScriptGC = true
fastReruns = true
enforceSerializableSessionState = false

[mapbox]
# Mapbox配置（如果使用地图功能）
token = ""

[deprecation]
# 弃用警告配置
showfileUploaderEncoding = false
showImageFormat = false
showPyplotGlobalUse = false
