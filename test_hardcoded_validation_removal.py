#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试硬编码验证逻辑是否已被移除
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hardcoded_validation_removal():
    """测试硬编码验证逻辑是否已被移除"""
    print("🧪 测试硬编码验证逻辑移除")
    print("=" * 60)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        from core.llm.tongyi_client import TongyiQianwenClient
        from core.utils.config import TongyiConfig
        
        # 创建实例（不需要真实配置）
        config = TongyiConfig(
            api_key="test",
            model="test",
            temperature=0.1,
            max_tokens=1000,
            enable_logging=False
        )
        
        # 创建LLM实例
        llm = EnhancedTongyiLLM(config)
        
        # 测试验证方法
        test_code = """
import pandas as pd
import streamlit as st

# 分析销售人员表现
salesperson_sales = df.groupby('销售员')['销售额'].sum()
st.bar_chart(salesperson_sales)
"""
        
        test_context = {
            'references': {
                'analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum()'
                }
            }
        }
        
        test_instruction = "在此基础上，分析销售人员销售额"
        
        print("🔍 测试代码验证方法:")
        print(f"输入代码: {test_code[:100]}...")
        print(f"指令: {test_instruction}")
        
        # 调用验证方法
        result_code = llm._validate_and_fix_reference_code(test_code, test_context, test_instruction)
        
        print(f"\n📊 验证结果:")
        print(f"输出代码长度: {len(result_code)} 字符")
        print(f"代码是否被修改: {'是' if result_code != test_code else '否'}")
        
        # 检查是否还有硬编码的验证逻辑
        hardcoded_checks = [
            "region_sales" in result_code and "region_sales" not in test_code,
            "groupby(['地区', '销售员'])" in result_code,
            "for region in region_sales['地区']:" in result_code
        ]
        
        has_hardcoded_injection = any(hardcoded_checks)
        
        print(f"\n✅ 硬编码检查:")
        print(f"  是否注入硬编码变量: {'是' if hardcoded_checks[0] else '否'} {'❌' if hardcoded_checks[0] else '✅'}")
        print(f"  是否强制特定分组: {'是' if hardcoded_checks[1] else '否'} {'❌' if hardcoded_checks[1] else '✅'}")
        print(f"  是否强制特定循环: {'是' if hardcoded_checks[2] else '否'} {'❌' if hardcoded_checks[2] else '✅'}")
        
        print(f"\n🎯 总体评估:")
        if not has_hardcoded_injection and result_code == test_code:
            print("✅ 优秀！验证逻辑已完全通用化")
            print("✅ 不再强制修改LLM生成的代码")
            print("✅ 移除了所有硬编码的验证规则")
        elif not has_hardcoded_injection:
            print("⚠️ 良好：无硬编码注入，但代码被修改")
            print("💡 建议：检查是否有其他修改逻辑")
        else:
            print("❌ 问题：仍然存在硬编码的验证逻辑")
            print("🔧 需要：进一步移除硬编码规则")
        
        return not has_hardcoded_injection
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_signature():
    """测试方法签名是否正确"""
    print("\n🔍 测试方法签名")
    print("=" * 40)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        import inspect
        
        # 检查方法是否存在
        method = getattr(EnhancedTongyiLLM, '_validate_and_fix_reference_code', None)
        if method is None:
            print("❌ 方法不存在")
            return False
        
        # 检查方法签名
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        
        print(f"方法参数: {params}")
        
        expected_params = ['self', 'code', 'conversation_context', 'instruction']
        has_correct_signature = params == expected_params
        
        print(f"签名正确: {'是' if has_correct_signature else '否'} {'✅' if has_correct_signature else '❌'}")
        
        return has_correct_signature
        
    except Exception as e:
        print(f"❌ 签名检查失败: {e}")
        return False

def test_source_code_analysis():
    """分析源代码，检查是否还有硬编码"""
    print("\n🔍 源代码硬编码分析")
    print("=" * 40)
    
    try:
        with open('core/llm/llm_factory.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 检查硬编码模式
        hardcoded_patterns = [
            "region_sales",
            "product_sales", 
            "groupby(['地区', '销售员'])",
            'groupby(["地区", "销售员"])',
            "for region in region_sales['地区']:",
            "❌ 缺少变量复用: 应该复用",
            "❌ 缺少基于展示: 应该使用"
        ]
        
        found_patterns = []
        for pattern in hardcoded_patterns:
            if pattern in source_code:
                # 计算出现次数
                count = source_code.count(pattern)
                found_patterns.append((pattern, count))
        
        print(f"检查的硬编码模式数量: {len(hardcoded_patterns)}")
        print(f"发现的硬编码模式数量: {len(found_patterns)}")
        
        if found_patterns:
            print(f"\n⚠️ 发现的硬编码模式:")
            for pattern, count in found_patterns:
                print(f"  - '{pattern}': {count} 次")
        else:
            print(f"\n✅ 未发现硬编码模式")
        
        # 检查是否还有强制修复逻辑
        has_forced_fix = "_generate_fixed_reference_code" in source_code
        print(f"\n强制修复方法存在: {'是' if has_forced_fix else '否'} {'❌' if has_forced_fix else '✅'}")
        
        is_clean = len(found_patterns) == 0 and not has_forced_fix
        
        print(f"\n🎯 源代码清洁度: {'优秀' if is_clean else '需要改进'} {'✅' if is_clean else '❌'}")
        
        return is_clean
        
    except Exception as e:
        print(f"❌ 源代码分析失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试硬编码验证逻辑移除")
    print("=" * 80)
    
    try:
        # 运行所有测试
        test1_passed = test_hardcoded_validation_removal()
        test2_passed = test_method_signature()
        test3_passed = test_source_code_analysis()
        
        print("\n" + "=" * 80)
        print("📊 测试结果汇总:")
        print(f"  功能测试: {'通过' if test1_passed else '失败'} {'✅' if test1_passed else '❌'}")
        print(f"  签名测试: {'通过' if test2_passed else '失败'} {'✅' if test2_passed else '❌'}")
        print(f"  源码分析: {'通过' if test3_passed else '失败'} {'✅' if test3_passed else '❌'}")
        
        all_passed = test1_passed and test2_passed and test3_passed
        
        print(f"\n🎉 总体结果: {'全部通过' if all_passed else '部分失败'} {'✅' if all_passed else '❌'}")
        
        if all_passed:
            print("\n🎯 优化成功！")
            print("✅ 硬编码验证逻辑已完全移除")
            print("✅ LLM生成的代码将不再被强制修改")
            print("✅ 系统现在完全依赖通用化的指导原则")
        else:
            print("\n⚠️ 仍需进一步优化")
            print("🔧 建议检查剩余的硬编码逻辑")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
