#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化方案测试用例 - 验证连续对话机制改进效果
"""

import json
from typing import Dict, List, Any
from datetime import datetime

# 模拟导入优化后的组件
# from 优化后的上下文管理器 import OptimizedContextManager, OptimizedLLMResponseProcessor

class TestCase:
    """测试用例基类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.results = {}
    
    def run(self):
        """运行测试用例"""
        raise NotImplementedError

def test_continuous_conversation_optimization():
    """
    测试连续对话优化效果
    """
    print("🧪 连续对话机制优化测试")
    print("=" * 60)
    
    # 模拟对话历史数据
    conversation_history = [
        {
            'user_message': '分析2024年各地区销售额',
            'assistant_message': '已完成地区销售额分析',
            'code': '''import numpy as np
import pandas as pd
import streamlit as st

# 确保数据类型正确
if 'df' in locals() or 'df' in globals():
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        df[numeric_cols] = df[numeric_cols].fillna(0)
    df = df.replace([np.inf, -np.inf], np.nan).fillna(0)

# 分析各地区销售额
region_sales = df.groupby('地区')['销售额'].sum().reset_index()

st.subheader("📊 2024年各地区销售额分析")
st.dataframe(region_sales)
st.bar_chart(region_sales, x='地区', y='销售额')''',
            'execution_result': {'success': True},
            'timestamp': '2025-08-05T17:08:19'
        }
    ]
    
    # 测试用例1：延续性对话
    test_continuation_analysis(conversation_history)
    
    # 测试用例2：修改性对话
    test_modification_analysis(conversation_history)
    
    # 测试用例3：对比性对话
    test_comparison_analysis(conversation_history)
    
    print("\n✅ 所有测试用例执行完成")

def test_continuation_analysis(conversation_history: List[Dict]):
    """测试延续性分析优化"""
    print("\n📋 测试用例1: 延续性分析")
    print("-" * 40)
    
    current_instruction = "在此基础上，分析各销售员的销售额"
    
    # 模拟优化前的提示词构建
    old_prompt = build_old_style_prompt(current_instruction, conversation_history)
    
    # 模拟优化后的提示词构建
    new_prompt = build_optimized_prompt(current_instruction, conversation_history)
    
    # 对比分析
    print("📊 对比结果:")
    print(f"  优化前提示词长度: {len(old_prompt)} 字符")
    print(f"  优化后提示词长度: {len(new_prompt)} 字符")
    print(f"  长度减少: {((len(old_prompt) - len(new_prompt)) / len(old_prompt) * 100):.1f}%")
    
    # 检查关键信息提取
    old_has_full_code = "import numpy as np" in old_prompt
    new_has_variables = "region_sales" in new_prompt and "import numpy as np" not in new_prompt
    
    print(f"  优化前包含完整代码: {'是' if old_has_full_code else '否'}")
    print(f"  优化后只包含关键变量: {'是' if new_has_variables else '否'}")
    
    # 引用检测测试
    reference_confidence = detect_reference_confidence(current_instruction, conversation_history)
    print(f"  引用检测置信度: {reference_confidence:.2f}")
    print(f"  引用检测结果: {'通过' if reference_confidence >= 0.5 else '未通过'}")

def test_modification_analysis(conversation_history: List[Dict]):
    """测试修改性分析优化"""
    print("\n🔧 测试用例2: 修改性分析")
    print("-" * 40)
    
    current_instruction = "修改上面的图表，使用饼图展示"
    
    # 引用检测
    reference_info = detect_reference_type(current_instruction, conversation_history)
    print(f"  检测到的引用类型: {reference_info['type']}")
    print(f"  置信度: {reference_info['confidence']:.2f}")
    
    # 指导原则生成
    guidance = generate_smart_guidance(reference_info, ['region_sales'], ['数据分组', '图表可视化'])
    print("  生成的指导原则:")
    for i, guide in enumerate(guidance[:3], 1):
        print(f"    {i}. {guide}")

def test_comparison_analysis(conversation_history: List[Dict]):
    """测试对比性分析优化"""
    print("\n⚖️ 测试用例3: 对比性分析")
    print("-" * 40)
    
    current_instruction = "对比各地区的销售额差异"
    
    # 测试代码验证功能
    mock_llm_response = '''```python
# 对比各地区销售额差异
region_comparison = df.groupby('地区')['销售额'].agg(['sum', 'mean', 'count']).reset_index()
st.subheader("📊 各地区销售额对比分析")
st.dataframe(region_comparison)
```'''
    
    # 代码提取测试
    extracted_code = extract_code_from_response(mock_llm_response)
    print(f"  代码提取成功: {'是' if extracted_code else '否'}")
    print(f"  提取的代码长度: {len(extracted_code)} 字符")
    
    # 代码质量检查
    quality_check = check_code_quality(extracted_code)
    print(f"  代码质量检查: {'通过' if quality_check['valid'] else '失败'}")
    if not quality_check['valid']:
        print(f"  问题: {quality_check['issues']}")

def build_old_style_prompt(instruction: str, conversation_history: List[Dict]) -> str:
    """构建旧式提示词（模拟现有实现）"""
    prompt_parts = [
        "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
        "",
        "最近的对话历史：",
    ]
    
    for i, round_data in enumerate(conversation_history, 1):
        prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
        if round_data.get('code'):
            prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{round_data['code']}\n```")
        prompt_parts.append(f"第{i}轮 - 代码执行：{'成功' if round_data.get('execution_result', {}).get('success') else '失败'}")
    
    prompt_parts.extend([
        "",
        f"当前用户问题：{instruction}",
        "",
        "🚨🚨🚨 CRITICAL MANDATORY TEMPLATE 🚨🚨🚨",
        "🔒 MANDATORY RULE: 必须按照以下模板生成代码...",
        "",
        "请基于以上对话历史和数据信息，生成相应的Python代码。"
    ])
    
    return "\n".join(prompt_parts)

def build_optimized_prompt(instruction: str, conversation_history: List[Dict]) -> str:
    """构建优化后的提示词"""
    # 提取关键信息
    key_variables = ['region_sales']
    key_operations = ['按地区分组', '求和计算', '图表可视化']
    reference_info = detect_reference_type(instruction, conversation_history)
    
    prompt_parts = [
        "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
        "",
        "=" * 50,
        "🔄 **对话上下文**",
        "=" * 50,
        f"**业务背景**: 用户正在进行地区分析，当前希望{instruction}",
        f"**引用类型**: {reference_info['type']} (置信度: {reference_info['confidence']:.2f})",
        f"**可用变量**: {', '.join(key_variables)}",
        f"**历史操作**: {', '.join(key_operations)}",
        "",
        "=" * 50,
        "🎯 **当前任务**",
        "=" * 50,
        f"**用户指令**: {instruction}",
        "",
        "=" * 50,
        "💡 **分析指导**",
        "=" * 50,
        "🔗 **延续性分析**: 用户希望基于之前的分析结果进行深入分析",
        "💡 **变量复用**: 可以直接使用已存在的变量: region_sales",
        "📋 **分析建议**: 在现有分析基础上增加新的维度或深度",
        "",
        "=" * 50,
        "📝 **请生成Python代码**",
        "=" * 50,
        "请基于以上信息生成相应的Python代码，确保代码能够独立运行并使用Streamlit组件展示结果。"
    ]
    
    return "\n".join(prompt_parts)

def detect_reference_confidence(instruction: str, conversation_history: List[Dict]) -> float:
    """检测引用置信度"""
    instruction_lower = instruction.lower()
    
    # 延续性模式匹配
    continuation_patterns = [
        r'(在.*基础上|基于.*|根据.*)',
        r'(进一步.*|深入.*|详细.*)',
        r'(接着.*|然后.*|继续.*)'
    ]
    
    confidence = 0.0
    pattern_matches = 0
    
    for pattern in continuation_patterns:
        import re
        if re.search(pattern, instruction_lower):
            pattern_matches += 1
    
    if pattern_matches > 0:
        confidence = 0.9 * (pattern_matches / len(continuation_patterns))
        
        # 上下文验证加分
        if conversation_history and conversation_history[-1].get('execution_result', {}).get('success'):
            confidence += 0.1
    
    return min(confidence, 1.0)

def detect_reference_type(instruction: str, conversation_history: List[Dict]) -> Dict[str, Any]:
    """检测引用类型"""
    instruction_lower = instruction.lower()
    
    if any(word in instruction_lower for word in ['基础上', '基于', '进一步']):
        return {'type': 'continuation', 'confidence': 0.85}
    elif any(word in instruction_lower for word in ['修改', '调整', '改变']):
        return {'type': 'modification', 'confidence': 0.90}
    elif any(word in instruction_lower for word in ['对比', '比较', '差异']):
        return {'type': 'comparison', 'confidence': 0.88}
    else:
        return {'type': 'independent', 'confidence': 0.1}

def generate_smart_guidance(reference_info: Dict, key_variables: List[str], key_operations: List[str]) -> List[str]:
    """生成智能指导原则"""
    guidance = []
    
    if reference_info['type'] == 'continuation':
        guidance.append("🔗 延续性分析: 用户希望基于之前的分析结果进行深入分析")
        if key_variables:
            guidance.append(f"💡 变量复用: 可以直接使用已存在的变量: {', '.join(key_variables)}")
        guidance.append("📋 分析建议: 在现有分析基础上增加新的维度或深度")
    elif reference_info['type'] == 'modification':
        guidance.append("🔧 修改优化: 用户希望调整或改进之前的分析")
        guidance.append("💡 改进方向: 保持核心逻辑，优化展示方式或分析角度")
    elif reference_info['type'] == 'comparison':
        guidance.append("⚖️ 对比分析: 用户希望进行比较分析")
        guidance.append("💡 对比建议: 使用相同的分析方法对不同维度进行对比")
    
    guidance.extend([
        "🛠️ 技术要求: 确保代码能够独立运行",
        "📊 展示要求: 使用Streamlit组件展示结果",
        "✨ 质量要求: 代码简洁高效，注重可读性"
    ])
    
    return guidance

def extract_code_from_response(response: str) -> str:
    """从LLM响应中提取代码"""
    import re
    
    # 提取```python代码块
    python_pattern = r'```python\s*\n(.*?)\n```'
    python_matches = re.findall(python_pattern, response, re.DOTALL)
    
    if python_matches:
        return python_matches[0].strip()
    
    return ""

def check_code_quality(code: str) -> Dict[str, Any]:
    """检查代码质量"""
    issues = []
    
    if not code.strip():
        issues.append("代码为空")
        return {'valid': False, 'issues': '; '.join(issues)}
    
    # 检查基本结构
    if not any(char in code for char in ['=', '(', ')']):
        issues.append("代码结构异常")
    
    # 检查Streamlit组件
    if 'st.' not in code:
        issues.append("缺少Streamlit展示组件")
    
    # 检查数据变量
    if 'df' not in code and 'data' not in code:
        issues.append("未使用数据变量df")
    
    return {
        'valid': len(issues) == 0,
        'issues': '; '.join(issues)
    }

if __name__ == "__main__":
    test_continuous_conversation_optimization()
