#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理模块
为PandasAI应用提供表格列名解释和元数据管理功能
帮助大语言模型更好地理解数据结构和业务含义
"""

import json
import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from dataclasses import dataclass, asdict
import re

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ColumnMetadata:
    """列元数据类"""
    name: str                    # 列名
    display_name: str           # 显示名称
    description: str            # 详细描述
    data_type: str             # 数据类型
    business_meaning: str      # 业务含义
    examples: List[str]        # 示例值
    constraints: Dict[str, Any] # 约束条件
    tags: List[str]            # 标签
    created_at: str            # 创建时间
    updated_at: str            # 更新时间

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()

@dataclass
class TableMetadata:
    """表格元数据类"""
    table_name: str                      # 表格名称
    description: str                     # 表格描述
    business_domain: str                 # 业务领域
    columns: Dict[str, ColumnMetadata]   # 列元数据
    relationships: Dict[str, str]        # 列间关系
    primary_keys: List[str]             # 主键列
    created_at: str                     # 创建时间
    updated_at: str                     # 更新时间
    version: str                        # 版本号

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
        if not self.version:
            self.version = "1.0.0"

class MetadataManager:
    """元数据管理器"""
    
    def __init__(self, config_dir: Union[str, Path] = "metadata_config"):
        """
        初始化元数据管理器

        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)

        # 创建完整的目录结构
        self._ensure_directory_structure()

        # 配置文件路径
        self.tables_config_file = self.config_dir / "tables_metadata.json"
        self.templates_config_file = self.config_dir / "column_templates.json"
        self.backups_dir = self.config_dir / "backups"
        self.logs_dir = self.config_dir / "logs"
        self.exports_dir = self.config_dir / "exports"

        # 加载配置
        self.tables_metadata: Dict[str, TableMetadata] = {}
        self.column_templates: Dict[str, Dict] = {}

        self._load_configurations()
        self._initialize_default_templates()

    def _ensure_directory_structure(self):
        """确保完整的目录结构存在"""
        subdirs = [
            "backups",      # 备份目录
            "logs",         # 日志目录
            "exports",      # 导出目录
            "examples",     # 示例目录
            "schemas"       # 模式定义目录
        ]

        for subdir in subdirs:
            (self.config_dir / subdir).mkdir(exist_ok=True)

        # 创建README文件（如果不存在）
        readme_file = self.config_dir / "README.md"
        if not readme_file.exists():
            self._create_readme_file(readme_file)

    def _create_readme_file(self, readme_path: Path):
        """创建README文件"""
        readme_content = """# 元数据配置目录

这个目录包含了PandasAI应用的表格元数据配置文件，用于帮助大语言模型更好地理解数据结构和业务含义。

## 📁 目录结构

```
metadata_config/
├── README.md                    # 说明文档
├── tables_metadata.json        # 表格元数据配置
├── column_templates.json       # 列模板配置
├── backups/                    # 配置备份
│   └── YYYY-MM-DD/
├── logs/                       # 操作日志
├── exports/                    # 导出报告
├── examples/                   # 配置示例
└── schemas/                    # 模式定义
```

## 📋 主要文件说明

### tables_metadata.json
存储所有已注册表格的元数据信息，包括：
- 表格基本信息（名称、描述、业务领域）
- 列详细信息（名称、描述、业务含义、数据类型、示例值等）
- 列间关系和约束条件
- 版本信息和时间戳

### column_templates.json
存储列模板配置，用于自动推断新列的元数据：
- 按业务领域分类的列模板
- 每个模板包含描述、业务含义、数据类型、约束条件等
- 支持关键词匹配和智能推荐

## 🔧 管理工具

使用命令行工具进行元数据管理：

```bash
# 查看当前状态
python metadata_management_tool.py status

# 清理重复元数据
python metadata_management_tool.py cleanup-duplicates

# 刷新元数据
python metadata_management_tool.py refresh

# 与文件系统同步
python metadata_management_tool.py sync

# 验证元数据
python metadata_management_tool.py validate

# 导出报告
python metadata_management_tool.py export
```

## 📊 最佳实践

1. **定期备份**: 系统会自动备份，也可手动备份重要配置
2. **定期清理**: 使用清理工具移除无用的元数据
3. **验证检查**: 定期验证元数据的完整性和一致性
4. **版本控制**: 重要变更建议进行版本控制

## 🛠️ 故障排除

- **配置文件损坏**: 从backups目录恢复最近的备份
- **元数据不一致**: 使用refresh命令重新同步
- **重复元数据**: 使用cleanup-duplicates命令清理

---
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        try:
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            logger.info(f"已创建README文件: {readme_path}")
        except Exception as e:
            logger.error(f"创建README文件失败: {e}")

    def create_backup(self, backup_name: str = None) -> Path:
        """
        创建元数据备份

        Args:
            backup_name: 备份名称，如果为None则使用时间戳

        Returns:
            Path: 备份文件路径
        """
        if backup_name is None:
            backup_name = f"metadata_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        backup_file = self.backups_dir / backup_name

        try:
            # 创建备份数据
            backup_data = {
                'backup_time': datetime.now().isoformat(),
                'tables_count': len(self.tables_metadata),
                'tables_metadata': {},
                'column_templates': self.column_templates
            }

            # 转换表格元数据为可序列化格式
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                # 转换列元数据为字典
                columns_dict = {}
                for col_name, col_metadata in table_metadata.columns.items():
                    columns_dict[col_name] = asdict(col_metadata)
                table_dict['columns'] = columns_dict
                backup_data['tables_metadata'][table_name] = table_dict

            # 保存备份
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"元数据备份已创建: {backup_file}")
            return backup_file

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            raise
    
    def _load_configurations(self):
        """加载配置文件"""
        try:
            # 加载表格元数据
            if self.tables_config_file.exists():
                with open(self.tables_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for table_name, table_data in data.items():
                        # 转换列元数据
                        columns = {}
                        for col_name, col_data in table_data.get('columns', {}).items():
                            columns[col_name] = ColumnMetadata(**col_data)
                        
                        table_data['columns'] = columns
                        self.tables_metadata[table_name] = TableMetadata(**table_data)
            
            # 加载列模板
            if self.templates_config_file.exists():
                with open(self.templates_config_file, 'r', encoding='utf-8') as f:
                    self.column_templates = json.load(f)
                    
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _save_configurations(self, create_backup: bool = False):
        """
        保存配置文件

        Args:
            create_backup: 是否在保存前创建备份
        """
        try:
            # 如果需要，创建备份
            if create_backup and len(self.tables_metadata) > 0:
                self.create_backup()

            # 保存表格元数据
            tables_data = {}
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                # 转换列元数据为字典
                columns_dict = {}
                for col_name, col_metadata in table_metadata.columns.items():
                    columns_dict[col_name] = asdict(col_metadata)
                table_dict['columns'] = columns_dict
                tables_data[table_name] = table_dict

            with open(self.tables_config_file, 'w', encoding='utf-8') as f:
                json.dump(tables_data, f, ensure_ascii=False, indent=2)

            # 保存列模板
            with open(self.templates_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.column_templates, f, ensure_ascii=False, indent=2)

            logger.debug("配置文件保存成功")

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息

        Returns:
            Dict: 存储相关信息
        """
        return {
            'config_directory': str(self.config_dir.absolute()),
            'tables_config_file': str(self.tables_config_file.absolute()),
            'templates_config_file': str(self.templates_config_file.absolute()),
            'backups_directory': str(self.backups_dir.absolute()),
            'logs_directory': str(self.logs_dir.absolute()),
            'exports_directory': str(self.exports_dir.absolute()),
            'total_tables': len(self.tables_metadata),
            'config_file_exists': self.tables_config_file.exists(),
            'templates_file_exists': self.templates_config_file.exists(),
            'directory_structure': {
                'backups': self.backups_dir.exists(),
                'logs': self.logs_dir.exists(),
                'exports': self.exports_dir.exists(),
                'examples': (self.config_dir / 'examples').exists(),
                'schemas': (self.config_dir / 'schemas').exists()
            }
        }
    
    def _initialize_default_templates(self):
        """初始化默认列模板"""
        if not self.column_templates:
            self.column_templates = {
                "销售相关": {
                    "销售额": {
                        "description": "产品或服务的销售金额",
                        "business_meaning": "反映业务收入情况的核心指标",
                        "data_type": "float",
                        "constraints": {"min": 0},
                        "tags": ["财务", "收入", "KPI"]
                    },
                    "销量": {
                        "description": "产品销售的数量",
                        "business_meaning": "反映产品市场接受度和需求量",
                        "data_type": "int",
                        "constraints": {"min": 0},
                        "tags": ["销售", "数量", "市场"]
                    },
                    "销售员": {
                        "description": "负责销售的员工姓名",
                        "business_meaning": "用于分析个人销售业绩和团队管理",
                        "data_type": "string",
                        "tags": ["人员", "业绩", "管理"]
                    }
                },
                "产品相关": {
                    "产品名称": {
                        "description": "产品的具体名称或型号",
                        "business_meaning": "用于产品分析和库存管理的标识",
                        "data_type": "string",
                        "tags": ["产品", "标识", "分类"]
                    },
                    "价格": {
                        "description": "产品的单价或售价",
                        "business_meaning": "定价策略和利润分析的基础数据",
                        "data_type": "float",
                        "constraints": {"min": 0},
                        "tags": ["定价", "财务", "策略"]
                    },
                    "库存": {
                        "description": "产品的库存数量",
                        "business_meaning": "库存管理和供应链优化的关键指标",
                        "data_type": "int",
                        "constraints": {"min": 0},
                        "tags": ["库存", "供应链", "管理"]
                    }
                },
                "地理相关": {
                    "地区": {
                        "description": "销售或业务发生的地理区域",
                        "business_meaning": "用于区域分析和市场策略制定",
                        "data_type": "string",
                        "tags": ["地理", "区域", "市场"]
                    },
                    "城市": {
                        "description": "具体的城市名称",
                        "business_meaning": "城市级别的市场分析和布局",
                        "data_type": "string",
                        "tags": ["地理", "城市", "市场"]
                    }
                },
                "时间相关": {
                    "日期": {
                        "description": "事件发生的具体日期",
                        "business_meaning": "时间序列分析和趋势预测的基础",
                        "data_type": "datetime",
                        "tags": ["时间", "趋势", "分析"]
                    },
                    "月份": {
                        "description": "事件发生的月份",
                        "business_meaning": "月度业绩分析和季节性趋势识别",
                        "data_type": "string",
                        "tags": ["时间", "月度", "季节性"]
                    }
                }
            }
            self._save_configurations()
    
    def register_table(self, table_name: str, df: pd.DataFrame,
                      description: str = "", business_domain: str = "",
                      use_smart_inference: bool = True) -> TableMetadata:
        """
        注册新表格并生成元数据

        Args:
            table_name: 表格名称
            df: DataFrame对象
            description: 表格描述
            business_domain: 业务领域
            use_smart_inference: 是否使用智能推断

        Returns:
            TableMetadata: 生成的表格元数据
        """
        logger.info(f"注册表格: {table_name}")

        if use_smart_inference:
            try:
                from metadata_inference import metadata_inference

                # 使用智能推断
                inferred_metadata = metadata_inference.infer_table_metadata(table_name, df)

                # 创建列元数据对象
                columns_metadata = {}
                for col_name, col_data in inferred_metadata["columns"].items():
                    columns_metadata[col_name] = ColumnMetadata(
                        name=col_data["name"],
                        display_name=col_data["display_name"],
                        description=col_data["description"],
                        data_type=col_data["data_type"],
                        business_meaning=col_data["business_meaning"],
                        examples=col_data["examples"],
                        constraints=col_data["constraints"],
                        tags=col_data["tags"],
                        created_at="",
                        updated_at=""
                    )

                # 创建表格元数据
                table_metadata = TableMetadata(
                    table_name=table_name,
                    description=description or inferred_metadata["description"],
                    business_domain=business_domain or inferred_metadata["business_domain"],
                    columns=columns_metadata,
                    relationships=inferred_metadata["relationships"],
                    primary_keys=inferred_metadata["primary_keys"],
                    created_at="",
                    updated_at="",
                    version=""
                )

                logger.info(f"使用智能推断注册表格 {table_name}，置信度: {inferred_metadata['inference_info']['confidence_score']:.2f}")

            except ImportError:
                logger.warning("智能推断模块不可用，使用基础推断")
                use_smart_inference = False

        if not use_smart_inference:
            # 使用基础推断
            columns_metadata = {}
            for col_name in df.columns:
                col_metadata = self._infer_column_metadata(col_name, df[col_name])
                columns_metadata[col_name] = col_metadata

            # 创建表格元数据
            table_metadata = TableMetadata(
                table_name=table_name,
                description=description or f"自动生成的{table_name}表格元数据",
                business_domain=business_domain or "通用",
                columns=columns_metadata,
                relationships={},
                primary_keys=[],
                created_at="",
                updated_at="",
                version=""
            )

        # 保存到内存和文件
        self.tables_metadata[table_name] = table_metadata
        self._save_configurations()

        logger.info(f"表格 {table_name} 注册成功，包含 {len(table_metadata.columns)} 列")
        return table_metadata

    def _infer_column_metadata(self, col_name: str, series: pd.Series) -> ColumnMetadata:
        """
        推断列元数据

        Args:
            col_name: 列名
            series: 列数据

        Returns:
            ColumnMetadata: 推断的列元数据
        """
        # 基本信息
        data_type = str(series.dtype)
        examples = []

        # 获取示例值（非空且唯一）
        non_null_values = series.dropna().unique()
        if len(non_null_values) > 0:
            examples = [str(val) for val in non_null_values[:3]]

        # 从模板匹配推断
        template_match = self._match_column_template(col_name, data_type)

        if template_match:
            return ColumnMetadata(
                name=col_name,
                display_name=template_match.get('display_name', col_name),
                description=template_match.get('description', f"{col_name}列"),
                data_type=data_type,
                business_meaning=template_match.get('business_meaning', "待定义"),
                examples=examples,
                constraints=template_match.get('constraints', {}),
                tags=template_match.get('tags', []),
                created_at="",
                updated_at=""
            )
        else:
            # 默认推断
            return ColumnMetadata(
                name=col_name,
                display_name=col_name,
                description=f"{col_name}列的数据",
                data_type=data_type,
                business_meaning="需要进一步定义业务含义",
                examples=examples,
                constraints={},
                tags=["未分类"],
                created_at="",
                updated_at=""
            )

    def _match_column_template(self, col_name: str, data_type: str) -> Optional[Dict]:
        """
        匹配列模板

        Args:
            col_name: 列名
            data_type: 数据类型

        Returns:
            匹配的模板字典或None
        """
        col_name_lower = col_name.lower()

        # 遍历所有模板类别
        for templates in self.column_templates.values():
            for template_name, template_config in templates.items():
                # 精确匹配
                if col_name == template_name:
                    return template_config

                # 包含匹配
                if template_name in col_name or col_name in template_name:
                    return template_config

                # 关键词匹配
                keywords = template_config.get('keywords', [template_name])
                for keyword in keywords:
                    if keyword.lower() in col_name_lower:
                        return template_config

        return None

    def get_table_metadata(self, table_name: str) -> Optional[TableMetadata]:
        """获取表格元数据"""
        return self.tables_metadata.get(table_name)

    def get_column_metadata(self, table_name: str, column_name: str) -> Optional[ColumnMetadata]:
        """获取列元数据"""
        table_metadata = self.get_table_metadata(table_name)
        if table_metadata:
            return table_metadata.columns.get(column_name)
        return None

    def update_column_metadata(self, table_name: str, column_name: str,
                             updates: Dict[str, Any]) -> bool:
        """
        更新列元数据

        Args:
            table_name: 表格名称
            column_name: 列名
            updates: 更新的字段

        Returns:
            bool: 更新是否成功
        """
        try:
            table_metadata = self.get_table_metadata(table_name)
            if not table_metadata:
                logger.error(f"表格 {table_name} 不存在")
                return False

            column_metadata = table_metadata.columns.get(column_name)
            if not column_metadata:
                logger.error(f"列 {column_name} 不存在于表格 {table_name}")
                return False

            # 更新字段
            for field, value in updates.items():
                if hasattr(column_metadata, field):
                    setattr(column_metadata, field, value)

            # 更新时间戳
            column_metadata.updated_at = datetime.now().isoformat()
            table_metadata.updated_at = datetime.now().isoformat()

            # 保存配置
            self._save_configurations()

            logger.info(f"列 {column_name} 元数据更新成功")
            return True

        except Exception as e:
            logger.error(f"更新列元数据失败: {e}")
            return False

    def generate_llm_context(self, table_name: str, df: pd.DataFrame) -> str:
        """
        为LLM生成表格上下文信息

        Args:
            table_name: 表格名称
            df: DataFrame对象

        Returns:
            str: 格式化的上下文信息
        """
        table_metadata = self.get_table_metadata(table_name)

        if not table_metadata:
            # 如果没有元数据，自动注册
            table_metadata = self.register_table(table_name, df)

        context_parts = []

        # 表格基本信息
        context_parts.append(f"表格名称: {table_metadata.table_name}")
        context_parts.append(f"业务领域: {table_metadata.business_domain}")
        context_parts.append(f"表格描述: {table_metadata.description}")
        context_parts.append("")

        # 列信息详解
        context_parts.append("列信息详解:")
        for col_name, col_metadata in table_metadata.columns.items():
            col_info = []
            col_info.append(f"- {col_name} ({col_metadata.display_name})")
            col_info.append(f"  描述: {col_metadata.description}")
            col_info.append(f"  业务含义: {col_metadata.business_meaning}")
            col_info.append(f"  数据类型: {col_metadata.data_type}")

            if col_metadata.examples:
                col_info.append(f"  示例值: {', '.join(col_metadata.examples[:3])}")

            if col_metadata.constraints:
                constraints_str = ', '.join([f"{k}={v}" for k, v in col_metadata.constraints.items()])
                col_info.append(f"  约束条件: {constraints_str}")

            if col_metadata.tags:
                col_info.append(f"  标签: {', '.join(col_metadata.tags)}")

            context_parts.extend(col_info)
            context_parts.append("")

        # 列间关系
        if table_metadata.relationships:
            context_parts.append("列间关系:")
            for rel_key, rel_desc in table_metadata.relationships.items():
                context_parts.append(f"- {rel_key}: {rel_desc}")
            context_parts.append("")

        # 主键信息
        if table_metadata.primary_keys:
            context_parts.append(f"主键列: {', '.join(table_metadata.primary_keys)}")
            context_parts.append("")

        return "\n".join(context_parts)

    def get_all_tables(self) -> List[str]:
        """获取所有已注册的表格名称"""
        return list(self.tables_metadata.keys())

    def export_metadata(self, output_file: Union[str, Path], format: str = "json") -> bool:
        """
        导出元数据配置

        Args:
            output_file: 输出文件路径
            format: 导出格式 (json/yaml)

        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_file)

            # 准备导出数据
            export_data = {
                "tables": {},
                "templates": self.column_templates,
                "export_time": datetime.now().isoformat(),
                "version": "1.0.0"
            }

            # 转换表格元数据
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                columns_dict = {}
                for col_name, col_metadata in table_metadata.columns.items():
                    columns_dict[col_name] = asdict(col_metadata)
                table_dict['columns'] = columns_dict
                export_data["tables"][table_name] = table_dict

            # 根据格式保存
            if format.lower() == "yaml":
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"元数据导出成功: {output_path}")
            return True

        except Exception as e:
            logger.error(f"导出元数据失败: {e}")
            return False

    def import_metadata(self, input_file: Union[str, Path]) -> bool:
        """
        导入元数据配置

        Args:
            input_file: 输入文件路径

        Returns:
            bool: 导入是否成功
        """
        try:
            input_path = Path(input_file)

            if not input_path.exists():
                logger.error(f"文件不存在: {input_path}")
                return False

            # 根据文件扩展名选择解析方式
            if input_path.suffix.lower() in ['.yaml', '.yml']:
                with open(input_path, 'r', encoding='utf-8') as f:
                    import_data = yaml.safe_load(f)
            else:
                with open(input_path, 'r', encoding='utf-8') as f:
                    import_data = json.load(f)

            # 导入模板
            if 'templates' in import_data:
                self.column_templates.update(import_data['templates'])

            # 导入表格元数据
            if 'tables' in import_data:
                for table_name, table_data in import_data['tables'].items():
                    # 转换列元数据
                    columns = {}
                    for col_name, col_data in table_data.get('columns', {}).items():
                        columns[col_name] = ColumnMetadata(**col_data)

                    table_data['columns'] = columns
                    self.tables_metadata[table_name] = TableMetadata(**table_data)

            # 保存配置
            self._save_configurations()

            logger.info(f"元数据导入成功: {input_path}")
            return True

        except Exception as e:
            logger.error(f"导入元数据失败: {e}")
            return False

    def add_column_template(self, category: str, template_name: str,
                           template_config: Dict[str, Any]) -> bool:
        """
        添加列模板

        Args:
            category: 模板类别
            template_name: 模板名称
            template_config: 模板配置

        Returns:
            bool: 添加是否成功
        """
        try:
            if category not in self.column_templates:
                self.column_templates[category] = {}

            self.column_templates[category][template_name] = template_config
            self._save_configurations()

            logger.info(f"添加列模板成功: {category}.{template_name}")
            return True

        except Exception as e:
            logger.error(f"添加列模板失败: {e}")
            return False

    def get_column_suggestions(self, col_name: str) -> List[Dict[str, Any]]:
        """
        获取列名建议

        Args:
            col_name: 列名

        Returns:
            List[Dict]: 建议列表
        """
        suggestions = []
        col_name_lower = col_name.lower()

        # 遍历所有模板寻找匹配
        for category, templates in self.column_templates.items():
            for template_name, template_config in templates.items():
                score = 0

                # 精确匹配
                if col_name == template_name:
                    score = 100
                # 包含匹配
                elif template_name in col_name or col_name in template_name:
                    score = 80
                # 关键词匹配
                else:
                    keywords = template_config.get('keywords', [template_name])
                    for keyword in keywords:
                        if keyword.lower() in col_name_lower:
                            score = max(score, 60)

                if score > 0:
                    suggestion = {
                        'category': category,
                        'template_name': template_name,
                        'score': score,
                        'config': template_config
                    }
                    suggestions.append(suggestion)

        # 按分数排序
        suggestions.sort(key=lambda x: x['score'], reverse=True)
        return suggestions[:5]  # 返回前5个建议

    def validate_metadata(self, table_name: str) -> Dict[str, List[str]]:
        """
        验证表格元数据的完整性

        Args:
            table_name: 表格名称

        Returns:
            Dict: 验证结果，包含错误和警告
        """
        result = {
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        table_metadata = self.get_table_metadata(table_name)
        if not table_metadata:
            result['errors'].append(f"表格 {table_name} 不存在")
            return result

        # 检查表格基本信息
        if not table_metadata.description or table_metadata.description.startswith("自动生成"):
            result['warnings'].append("表格描述需要完善")

        if not table_metadata.business_domain or table_metadata.business_domain == "通用":
            result['warnings'].append("建议指定具体的业务领域")

        # 检查列元数据
        for col_name, col_metadata in table_metadata.columns.items():
            if col_metadata.business_meaning == "需要进一步定义业务含义":
                result['warnings'].append(f"列 {col_name} 的业务含义需要定义")

            if not col_metadata.examples:
                result['suggestions'].append(f"建议为列 {col_name} 添加示例值")

            if col_metadata.tags == ["未分类"]:
                result['suggestions'].append(f"建议为列 {col_name} 添加合适的标签")

        return result

    def cleanup_test_tables(self) -> Dict[str, int]:
        """清理测试表格，只保留用户真正上传的表格"""
        # 识别测试表格的模式
        test_patterns = [
            'test', 'demo', 'sample', 'example', 'temp', 'tmp',
            'sales_data', 'finance_data', 'inventory_data',
            'metadata_test', 'ui_test', 'save_test', 'batch_test',
            'customer_sales', 'product_inventory', 'meaningful_table',
            'cryptic_table', 'customer_info_single_table', 'sales_metadata_test',
            'customer_data', 'product_data', 'order_data', 'sales_with_relationships'
        ]

        tables_to_delete = []

        for table_name in list(self.tables_metadata.keys()):
            # 检查是否匹配测试模式
            is_test_table = any(pattern in table_name.lower() for pattern in test_patterns)

            if is_test_table:
                tables_to_delete.append(table_name)

        # 执行删除
        deleted_count = 0
        for table_name in tables_to_delete:
            try:
                del self.tables_metadata[table_name]
                deleted_count += 1
                logger.info(f"已删除测试表格: {table_name}")
            except Exception as e:
                logger.error(f"删除表格 {table_name} 失败: {e}")

        # 保存清理后的元数据
        if deleted_count > 0:
            self._save_configurations()

        return {
            'deleted': deleted_count,
            'remaining': len(self.tables_metadata),
            'deleted_tables': tables_to_delete
        }

    def refresh_metadata(self, data_directory: Union[str, Path] = "uploaded_files") -> Dict[str, Any]:
        """
        刷新元数据，清理孤立元数据并更新现有表格

        Args:
            data_directory: 数据文件目录

        Returns:
            Dict: 刷新结果统计
        """
        logger.info("开始刷新元数据...")

        data_dir = Path(data_directory)
        if not data_dir.exists():
            data_dir.mkdir(exist_ok=True)

        # 获取实际存在的数据文件
        existing_files = self._scan_data_files(data_dir)

        # 获取当前元数据中的表格
        metadata_tables = set(self.tables_metadata.keys())

        # 识别孤立的元数据（没有对应数据文件的元数据）
        orphaned_metadata = self._identify_orphaned_metadata(metadata_tables, existing_files)

        # 识别需要更新的表格（数据文件存在但元数据过期）
        tables_to_update = self._identify_outdated_metadata(existing_files)

        # 识别新的数据文件（有数据文件但没有元数据）
        new_files = self._identify_new_files(existing_files, metadata_tables)

        # 执行清理和更新
        cleanup_result = self._cleanup_orphaned_metadata(orphaned_metadata)
        update_result = self._update_outdated_metadata(tables_to_update)
        register_result = self._register_new_files(new_files, data_dir)

        # 保存更新后的配置
        self._save_configurations()

        result = {
            'scanned_files': len(existing_files),
            'orphaned_cleaned': cleanup_result['cleaned'],
            'tables_updated': update_result['updated'],
            'new_files_registered': register_result['registered'],
            'total_tables': len(self.tables_metadata),
            'orphaned_tables': orphaned_metadata,
            'updated_tables': list(tables_to_update.keys()),
            'new_tables': list(new_files.keys())
        }

        logger.info(f"元数据刷新完成: {result}")
        return result

    def _scan_data_files(self, data_dir: Path) -> Dict[str, Dict[str, Any]]:
        """扫描数据目录中的文件"""
        files_info = {}

        # 支持的文件格式
        supported_extensions = {'.csv', '.xlsx', '.xls', '.json', '.txt'}

        for file_path in data_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                # 生成表格名（去除扩展名）
                table_name = file_path.stem

                files_info[table_name] = {
                    'file_path': file_path,
                    'file_name': file_path.name,
                    'modified_time': file_path.stat().st_mtime,
                    'size': file_path.stat().st_size
                }

                # 同时记录带扩展名的版本（处理历史兼容性）
                table_name_with_ext = file_path.name
                if table_name_with_ext != table_name:
                    files_info[table_name_with_ext] = files_info[table_name].copy()

        return files_info

    def _identify_orphaned_metadata(self, metadata_tables: set, existing_files: Dict[str, Any]) -> List[str]:
        """识别孤立的元数据"""
        orphaned = []

        for table_name in metadata_tables:
            # 检查是否有对应的数据文件
            if table_name not in existing_files:
                # 检查是否是带扩展名的表格名，尝试匹配不带扩展名的版本
                base_name = table_name.rsplit('.', 1)[0] if '.' in table_name else table_name
                if base_name not in existing_files:
                    orphaned.append(table_name)

        return orphaned

    def _identify_outdated_metadata(self, existing_files: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """识别需要更新的元数据"""
        outdated = {}

        for table_name, file_info in existing_files.items():
            if table_name in self.tables_metadata:
                metadata = self.tables_metadata[table_name]

                # 检查文件修改时间是否晚于元数据更新时间
                try:
                    metadata_time = datetime.fromisoformat(metadata.updated_at).timestamp()
                    file_time = file_info['modified_time']

                    if file_time > metadata_time:
                        outdated[table_name] = file_info
                except Exception as e:
                    logger.warning(f"无法比较时间戳 {table_name}: {e}")
                    # 如果时间戳比较失败，标记为需要更新
                    outdated[table_name] = file_info

        return outdated

    def _identify_new_files(self, existing_files: Dict[str, Any], metadata_tables: set) -> Dict[str, Dict[str, Any]]:
        """识别新的数据文件"""
        new_files = {}

        for table_name, file_info in existing_files.items():
            if table_name not in metadata_tables:
                # 检查是否是重复的扩展名版本
                base_name = table_name.rsplit('.', 1)[0] if '.' in table_name else table_name
                if base_name not in metadata_tables:
                    new_files[table_name] = file_info

        return new_files

    def refresh_metadata(self, data_directory: Union[str, Path] = "uploaded_files") -> Dict[str, Any]:
        """
        刷新元数据，清理孤立元数据并更新现有表格

        Args:
            data_directory: 数据文件目录

        Returns:
            Dict: 刷新结果统计
        """
        logger.info("开始刷新元数据...")

        data_dir = Path(data_directory)
        if not data_dir.exists():
            data_dir.mkdir(exist_ok=True)

        # 获取实际存在的数据文件
        existing_files = self._scan_data_files(data_dir)

        # 获取当前元数据中的表格
        metadata_tables = set(self.tables_metadata.keys())

        # 识别孤立的元数据（没有对应数据文件的元数据）
        orphaned_metadata = self._identify_orphaned_metadata(metadata_tables, existing_files)

        # 识别需要更新的表格（数据文件存在但元数据过期）
        tables_to_update = self._identify_outdated_metadata(existing_files)

        # 识别新的数据文件（有数据文件但没有元数据）
        new_files = self._identify_new_files(existing_files, metadata_tables)

        # 执行清理和更新
        cleanup_result = self._cleanup_orphaned_metadata(orphaned_metadata)
        update_result = self._update_outdated_metadata(tables_to_update)
        register_result = self._register_new_files(new_files, data_dir)

        # 保存更新后的配置
        self._save_configurations()

        result = {
            'scanned_files': len(existing_files),
            'orphaned_cleaned': cleanup_result['cleaned'],
            'tables_updated': update_result['updated'],
            'new_files_registered': register_result['registered'],
            'total_tables': len(self.tables_metadata),
            'orphaned_tables': orphaned_metadata,
            'updated_tables': list(tables_to_update.keys()),
            'new_tables': list(new_files.keys())
        }

        logger.info(f"元数据刷新完成: {result}")
        return result

    def _scan_data_files(self, data_dir: Path) -> Dict[str, Dict[str, Any]]:
        """扫描数据目录中的文件"""
        files_info = {}

        # 支持的文件格式
        supported_extensions = {'.csv', '.xlsx', '.xls', '.json', '.txt'}

        for file_path in data_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                # 生成表格名（去除扩展名）
                table_name = file_path.stem

                files_info[table_name] = {
                    'file_path': file_path,
                    'file_name': file_path.name,
                    'modified_time': file_path.stat().st_mtime,
                    'size': file_path.stat().st_size
                }

                # 同时记录带扩展名的版本（处理历史兼容性）
                table_name_with_ext = file_path.name
                if table_name_with_ext != table_name:
                    files_info[table_name_with_ext] = files_info[table_name].copy()

        return files_info

    def _identify_orphaned_metadata(self, metadata_tables: set, existing_files: Dict[str, Any]) -> List[str]:
        """识别孤立的元数据"""
        orphaned = []

        for table_name in metadata_tables:
            # 检查是否有对应的数据文件
            if table_name not in existing_files:
                # 检查是否是带扩展名的表格名，尝试匹配不带扩展名的版本
                base_name = table_name.rsplit('.', 1)[0] if '.' in table_name else table_name
                if base_name not in existing_files:
                    orphaned.append(table_name)

        return orphaned

    def _identify_outdated_metadata(self, existing_files: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """识别需要更新的元数据"""
        outdated = {}

        for table_name, file_info in existing_files.items():
            if table_name in self.tables_metadata:
                metadata = self.tables_metadata[table_name]

                # 检查文件修改时间是否晚于元数据更新时间
                try:
                    metadata_time = datetime.fromisoformat(metadata.updated_at).timestamp()
                    file_time = file_info['modified_time']

                    if file_time > metadata_time:
                        outdated[table_name] = file_info
                except Exception as e:
                    logger.warning(f"无法比较时间戳 {table_name}: {e}")
                    # 如果时间戳比较失败，标记为需要更新
                    outdated[table_name] = file_info

        return outdated

    def _identify_new_files(self, existing_files: Dict[str, Any], metadata_tables: set) -> Dict[str, Dict[str, Any]]:
        """识别新的数据文件"""
        new_files = {}

        for table_name, file_info in existing_files.items():
            if table_name not in metadata_tables:
                # 检查是否是重复的扩展名版本
                base_name = table_name.rsplit('.', 1)[0] if '.' in table_name else table_name
                if base_name not in metadata_tables:
                    new_files[table_name] = file_info

        return new_files

    def _cleanup_orphaned_metadata(self, orphaned_metadata: List[str]) -> Dict[str, Any]:
        """清理孤立的元数据"""
        cleaned_count = 0
        failed_count = 0

        for table_name in orphaned_metadata:
            try:
                if table_name in self.tables_metadata:
                    del self.tables_metadata[table_name]
                    cleaned_count += 1
                    logger.info(f"已清理孤立元数据: {table_name}")
            except Exception as e:
                logger.error(f"清理孤立元数据失败 {table_name}: {e}")
                failed_count += 1

        return {
            'cleaned': cleaned_count,
            'failed': failed_count
        }

    def _update_outdated_metadata(self, tables_to_update: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """更新过期的元数据"""
        updated_count = 0
        failed_count = 0

        for table_name, file_info in tables_to_update.items():
            try:
                # 重新读取数据文件
                df = self._load_dataframe(file_info['file_path'])
                if df is not None:
                    # 重新注册表格（这会更新元数据）
                    self.register_table(table_name, df, use_smart_inference=True)
                    updated_count += 1
                    logger.info(f"已更新过期元数据: {table_name}")
            except Exception as e:
                logger.error(f"更新元数据失败 {table_name}: {e}")
                failed_count += 1

        return {
            'updated': updated_count,
            'failed': failed_count
        }

    def _register_new_files(self, new_files: Dict[str, Dict[str, Any]], data_dir: Path) -> Dict[str, Any]:
        """注册新的数据文件"""
        registered_count = 0
        failed_count = 0

        for table_name, file_info in new_files.items():
            try:
                # 读取数据文件
                df = self._load_dataframe(file_info['file_path'])
                if df is not None:
                    # 注册新表格
                    self.register_table(table_name, df, use_smart_inference=True)
                    registered_count += 1
                    logger.info(f"已注册新文件: {table_name}")
            except Exception as e:
                logger.error(f"注册新文件失败 {table_name}: {e}")
                failed_count += 1

        return {
            'registered': registered_count,
            'failed': failed_count
        }

    def _load_dataframe(self, file_path: Path) -> Optional[pd.DataFrame]:
        """加载数据文件为DataFrame"""
        try:
            file_extension = file_path.suffix.lower()

            if file_extension == '.csv':
                return pd.read_csv(file_path, encoding='utf-8')
            elif file_extension in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_extension == '.json':
                return pd.read_json(file_path)
            elif file_extension == '.txt':
                # 尝试作为CSV读取
                return pd.read_csv(file_path, sep='\t', encoding='utf-8')
            else:
                logger.warning(f"不支持的文件格式: {file_extension}")
                return None

        except Exception as e:
            logger.error(f"加载数据文件失败 {file_path}: {e}")
            return None

    def cleanup_duplicate_metadata(self) -> Dict[str, Any]:
        """清理重复的元数据条目"""
        logger.info("开始清理重复的元数据...")

        # 识别重复的表格（如 sales_data 和 sales_data.csv）
        duplicates = {}
        tables_to_remove = []

        for table_name in list(self.tables_metadata.keys()):
            # 检查是否有对应的不带扩展名的版本
            if '.' in table_name:
                base_name = table_name.rsplit('.', 1)[0]
                if base_name in self.tables_metadata and base_name != table_name:
                    # 找到重复项，保留较新的版本
                    base_metadata = self.tables_metadata[base_name]
                    ext_metadata = self.tables_metadata[table_name]

                    try:
                        base_time = datetime.fromisoformat(base_metadata.updated_at)
                        ext_time = datetime.fromisoformat(ext_metadata.updated_at)

                        if base_time >= ext_time:
                            # 保留base版本，删除带扩展名的版本
                            tables_to_remove.append(table_name)
                            duplicates[table_name] = base_name
                        else:
                            # 保留带扩展名的版本，删除base版本
                            tables_to_remove.append(base_name)
                            duplicates[base_name] = table_name
                    except Exception as e:
                        logger.warning(f"比较时间戳失败，保留base版本: {e}")
                        tables_to_remove.append(table_name)
                        duplicates[table_name] = base_name

        # 执行删除
        removed_count = 0
        for table_name in tables_to_remove:
            try:
                if table_name in self.tables_metadata:
                    del self.tables_metadata[table_name]
                    removed_count += 1
                    logger.info(f"已删除重复元数据: {table_name}")
            except Exception as e:
                logger.error(f"删除重复元数据失败 {table_name}: {e}")

        # 保存配置
        if removed_count > 0:
            self._save_configurations()

        return {
            'duplicates_found': len(duplicates),
            'duplicates_removed': removed_count,
            'duplicate_pairs': duplicates
        }

    def sync_with_filesystem(self, data_directory: Union[str, Path] = "uploaded_files",
                           demo_file: Union[str, Path] = "demo_data.csv") -> Dict[str, Any]:
        """
        与文件系统同步，包括上传文件目录和演示数据文件

        Args:
            data_directory: 上传文件目录
            demo_file: 演示数据文件路径

        Returns:
            Dict: 同步结果
        """
        logger.info("开始与文件系统同步...")

        # 首先清理重复的元数据
        duplicate_result = self.cleanup_duplicate_metadata()

        # 刷新上传文件目录的元数据
        refresh_result = self.refresh_metadata(data_directory)

        # 处理演示数据文件
        demo_result = self._sync_demo_file(demo_file)

        # 合并结果
        total_result = {
            'duplicate_cleanup': duplicate_result,
            'directory_refresh': refresh_result,
            'demo_sync': demo_result,
            'total_tables_after_sync': len(self.tables_metadata)
        }

        logger.info(f"文件系统同步完成: {total_result}")
        return total_result

    def _sync_demo_file(self, demo_file: Union[str, Path]) -> Dict[str, Any]:
        """同步演示数据文件"""
        demo_path = Path(demo_file)

        if not demo_path.exists():
            return {
                'demo_file_exists': False,
                'demo_registered': False,
                'message': f'演示文件不存在: {demo_path}'
            }

        try:
            # 读取演示数据
            df = self._load_dataframe(demo_path)
            if df is not None:
                table_name = demo_path.stem  # 不带扩展名的文件名

                # 检查是否已经注册
                if table_name not in self.tables_metadata:
                    # 注册演示数据
                    self.register_table(
                        table_name,
                        df,
                        description=f"演示数据表，包含{len(df)}条记录和{len(df.columns)}个字段",
                        business_domain="演示数据",
                        use_smart_inference=True
                    )
                    return {
                        'demo_file_exists': True,
                        'demo_registered': True,
                        'table_name': table_name,
                        'records': len(df),
                        'columns': len(df.columns)
                    }
                else:
                    return {
                        'demo_file_exists': True,
                        'demo_registered': False,
                        'message': f'演示数据 {table_name} 已存在'
                    }
            else:
                return {
                    'demo_file_exists': True,
                    'demo_registered': False,
                    'message': '无法读取演示数据文件'
                }

        except Exception as e:
            logger.error(f"同步演示文件失败: {e}")
            return {
                'demo_file_exists': True,
                'demo_registered': False,
                'error': str(e)
            }

# 创建全局实例
metadata_manager = MetadataManager()
