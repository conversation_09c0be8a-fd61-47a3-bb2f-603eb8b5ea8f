# AI数据分析平台 - 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# =============================================================================
# 应用基础配置
# =============================================================================
ENVIRONMENT=development
DEBUG=false

# =============================================================================
# LLM配置 - 通义千问
# =============================================================================
# 必填：通义千问API密钥
TONGYI_API_KEY=your_api_key_here

# 可选：模型配置
TONGYI_MODEL=qwen-plus
TONGYI_TEMPERATURE=0.1
TONGYI_MAX_TOKENS=2000
TONGYI_TIMEOUT=30

# 功能开关
ENABLE_CHART_FIX=true
ENABLE_METADATA=true
ENABLE_LOGGING=true

# 重试配置
RETRY_ATTEMPTS=3
RETRY_DELAY=1.0

# =============================================================================
# Streamlit配置
# =============================================================================
STREAMLIT_PORT=8501
STREAMLIT_ADDRESS=localhost
STREAMLIT_HEADLESS=false

# =============================================================================
# 数据处理配置
# =============================================================================
# 文件上传限制（MB）
MAX_FILE_SIZE_MB=200

# 数据处理选项
AUTO_CLEAN_DATA=true
MAX_ROWS_PREVIEW=1000

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_BACKUP_COUNT=5

# =============================================================================
# 安全配置
# =============================================================================
REQUIRE_API_KEY=true
ALLOW_FILE_OPERATIONS=false
ALLOW_NETWORK_OPERATIONS=false
SESSION_TIMEOUT_MINUTES=60

# =============================================================================
# 性能配置
# =============================================================================
ENABLE_CACHING=true
CACHE_TTL_SECONDS=3600
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=30

# =============================================================================
# 开发配置（仅开发环境使用）
# =============================================================================
# 开发模式下的额外配置
DEV_AUTO_RELOAD=true
DEV_SHOW_DEBUG_INFO=false
DEV_MOCK_API_CALLS=false
