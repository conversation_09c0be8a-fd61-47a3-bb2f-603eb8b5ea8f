"""
Validation utilities for the data analysis platform.
"""

import re
import ast
from typing import Tuple, List, Optional
import pandas as pd


def validate_api_key(api_key: str) -> Tuple[bool, str]:
    """
    验证API密钥格式
    
    Args:
        api_key: API密钥
    
    Returns:
        (是否有效, 错误信息)
    """
    if not api_key:
        return False, "API密钥不能为空"
    
    if len(api_key) < 10:
        return False, "API密钥长度过短"
    
    # 基本格式检查（可根据实际API密钥格式调整）
    if not re.match(r'^[a-zA-Z0-9\-_]+$', api_key):
        return False, "API密钥格式无效"
    
    return True, ""


def validate_code(code: str) -> Tuple[bool, str, List[str]]:
    """
    验证生成的Python代码
    
    Args:
        code: Python代码字符串
    
    Returns:
        (是否有效, 错误信息, 警告列表)
    """
    if not code.strip():
        return False, "代码不能为空", []
    
    warnings = []
    
    # 语法检查
    try:
        ast.parse(code)
    except SyntaxError as e:
        return False, f"语法错误: {e}", warnings
    
    # 安全检查 - 禁止的操作
    dangerous_patterns = [
        r'import\s+os',
        r'import\s+subprocess',
        r'import\s+sys',
        r'__import__',
        r'eval\s*\(',
        r'exec\s*\(',
        r'open\s*\(',
        r'file\s*\(',
        r'input\s*\(',
        r'raw_input\s*\(',
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, f"代码包含不安全的操作: {pattern}", warnings
    
    # 检查是否包含必要的导入
    if 'import pandas' not in code and 'pd.' in code:
        warnings.append("代码使用了pandas但未导入")
    
    if 'import matplotlib' not in code and 'plt.' in code:
        warnings.append("代码使用了matplotlib但未导入")
    
    return True, "", warnings


def validate_dataframe(df: pd.DataFrame) -> Tuple[bool, str, List[str]]:
    """
    验证DataFrame的质量
    
    Args:
        df: 要验证的DataFrame
    
    Returns:
        (是否有效, 错误信息, 警告列表)
    """
    if df is None:
        return False, "DataFrame不能为None", []
    
    if df.empty:
        return False, "DataFrame不能为空", []
    
    warnings = []
    
    # 检查缺失值
    missing_ratio = df.isnull().sum().sum() / (df.shape[0] * df.shape[1])
    if missing_ratio > 0.5:
        warnings.append(f"数据缺失率过高: {missing_ratio:.2%}")
    elif missing_ratio > 0.1:
        warnings.append(f"数据存在缺失值: {missing_ratio:.2%}")
    
    # 检查重复行
    duplicate_ratio = df.duplicated().sum() / len(df)
    if duplicate_ratio > 0.1:
        warnings.append(f"重复行比例较高: {duplicate_ratio:.2%}")
    
    # 检查数据类型 - 移除文本列提示，不需要显示给用户
    object_cols = df.select_dtypes(include=['object']).columns
    # 不再显示文本列信息，这是正常的数据类型
    
    return True, "", warnings


def validate_file_upload(file_path: str, max_size_mb: int = 200) -> Tuple[bool, str]:
    """
    验证上传的文件
    
    Args:
        file_path: 文件路径
        max_size_mb: 最大文件大小（MB）
    
    Returns:
        (是否有效, 错误信息)
    """
    import os
    
    if not os.path.exists(file_path):
        return False, "文件不存在"
    
    # 检查文件大小
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
    if file_size_mb > max_size_mb:
        return False, f"文件过大: {file_size_mb:.1f}MB (最大: {max_size_mb}MB)"
    
    # 检查文件扩展名
    supported_extensions = ['.csv', '.xlsx', '.xls', '.json', '.txt']
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in supported_extensions:
        return False, f"不支持的文件类型: {file_ext}"
    
    return True, ""
