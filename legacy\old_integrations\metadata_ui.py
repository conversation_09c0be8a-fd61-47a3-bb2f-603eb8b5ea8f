#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理界面模块
为Streamlit应用提供可视化的元数据管理功能
"""

import streamlit as st
import pandas as pd
from metadata_manager import metadata_manager, ColumnMetadata, TableMetadata
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

class MetadataUI:
    """元数据管理界面类"""
    
    @staticmethod
    def render_metadata_management():
        """渲染元数据管理主界面"""
        st.header("🎯 元数据管理")
        st.markdown("管理表格和列的元数据信息，提升AI分析准确性")
        
        # 创建标签页 - 将列管理放在第一位（核心功能）
        tab1, tab2, tab3, tab4 = st.tabs(["📋 列管理", "📊 表格管理", "🔧 模板管理", "📤 导入导出"])

        with tab1:
            MetadataUI._render_column_management()

        with tab2:
            MetadataUI._render_table_management()

        with tab3:
            MetadataUI._render_template_management()

        with tab4:
            MetadataUI._render_import_export()
    
    @staticmethod
    def _render_table_management():
        """渲染表格管理界面"""
        st.subheader("📊 表格元数据管理")

        # 添加引导提示
        st.warning("💡 **建议**: 表格管理主要用于组织和配置表级信息。如需提升AI查询准确性，请优先完善「📋 列管理」中的列描述和业务含义！")

        # 获取所有表格
        tables = metadata_manager.get_all_tables()
        
        if not tables:
            st.info("暂无已注册的表格。上传数据文件后会自动注册表格元数据。")
            return
        
        # 选择表格
        selected_table = st.selectbox("选择表格", tables, key="table_select")
        
        if selected_table:
            table_metadata = metadata_manager.get_table_metadata(selected_table)
            
            if table_metadata:
                # 显示表格基本信息
                col1, col2 = st.columns(2)
                
                with col1:
                    st.metric("表格名称", table_metadata.table_name)
                    st.metric("列数量", len(table_metadata.columns))
                    st.metric("版本", table_metadata.version)
                
                with col2:
                    st.metric("业务领域", table_metadata.business_domain)
                    st.metric("创建时间", table_metadata.created_at[:10])
                    st.metric("更新时间", table_metadata.updated_at[:10])
                
                # 表格描述编辑
                st.subheader("📝 表格描述")
                new_description = st.text_area(
                    "描述",
                    value=table_metadata.description,
                    height=100,
                    key=f"desc_{selected_table}"
                )
                
                # 业务领域编辑
                business_domains = ["销售管理", "财务分析", "库存管理", "客户管理", "产品分析", "市场营销", "人力资源", "通用"]
                current_domain_index = business_domains.index(table_metadata.business_domain) if table_metadata.business_domain in business_domains else 0
                
                new_business_domain = st.selectbox(
                    "业务领域",
                    business_domains,
                    index=current_domain_index,
                    key=f"domain_{selected_table}"
                )
                
                # 主键设置
                st.subheader("🔑 主键设置")
                column_names = list(table_metadata.columns.keys())
                current_primary_keys = table_metadata.primary_keys or []
                
                new_primary_keys = st.multiselect(
                    "选择主键列",
                    column_names,
                    default=current_primary_keys,
                    key=f"pk_{selected_table}"
                )
                
                # 列间关系 - 改进的结构化配置
                st.subheader("🔗 列间关系配置")

                # 获取当前表格的所有列名
                column_names = list(table_metadata.columns.keys())

                # 预设关系类型和模板
                relationship_types = [
                    "计算关系", "分组关系", "约束关系", "依赖关系",
                    "层级关系", "时间关系", "业务关系", "自定义关系"
                ]

                # 关系模板
                relationship_templates = {
                    "计算关系": {
                        "销售额_单价_销量": "销售额 = 单价 × 销量",
                        "利润_收入_成本": "利润 = 收入 - 成本",
                        "总价_数量_单价": "总价 = 数量 × 单价",
                        "增长率_当期_上期": "增长率 = (当期 - 上期) / 上期 × 100%"
                    },
                    "分组关系": {
                        "地区_销售额": "按地区统计销售额",
                        "产品_销量": "按产品分组统计销量",
                        "时间_指标": "按时间维度分组分析"
                    },
                    "约束关系": {
                        "库存_销量": "库存数量必须大于等于销量",
                        "开始时间_结束时间": "开始时间必须早于结束时间",
                        "最小值_最大值": "最小值必须小于最大值"
                    },
                    "依赖关系": {
                        "订单_客户": "订单依赖于客户信息",
                        "明细_主表": "明细记录依赖于主表记录",
                        "子项_父项": "子项依赖于父项存在"
                    }
                }

                # 显示现有关系
                if table_metadata.relationships:
                    st.write("**当前配置的关系：**")
                    for rel_key, rel_desc in table_metadata.relationships.items():
                        st.info(f"🔗 {rel_key}: {rel_desc}")

                # 关系配置区域
                with st.expander("➕ 添加/编辑列间关系", expanded=len(table_metadata.relationships) == 0):
                    # 使用会话状态存储关系配置
                    if f"relationships_{selected_table}" not in st.session_state:
                        st.session_state[f"relationships_{selected_table}"] = []

                    # 添加新关系按钮
                    col1, col2 = st.columns([3, 1])
                    with col2:
                        if st.button("➕ 添加关系", key=f"add_rel_{selected_table}"):
                            st.session_state[f"relationships_{selected_table}"].append({
                                "source_column": "",
                                "target_columns": [],
                                "relationship_type": "计算关系",
                                "description": ""
                            })

                    # 显示关系配置表单
                    relationships_config = st.session_state[f"relationships_{selected_table}"]

                    for i, rel_config in enumerate(relationships_config):
                        st.write(f"**关系配置 {i+1}:**")

                        col1, col2, col3 = st.columns([2, 2, 1])

                        with col1:
                            # 源列选择
                            source_column = st.selectbox(
                                "源列",
                                [""] + column_names,
                                index=column_names.index(rel_config["source_column"]) + 1 if rel_config["source_column"] in column_names else 0,
                                key=f"source_{selected_table}_{i}",
                                help="选择作为关系起点的列"
                            )
                            rel_config["source_column"] = source_column

                        with col2:
                            # 根据关系类型决定是否需要目标列
                            relationship_type = rel_config.get("relationship_type", "计算关系")

                            # 需要关联列的关系类型
                            needs_targets = relationship_type in ["计算关系", "约束关系", "依赖关系"]

                            if needs_targets:
                                # 目标列多选（必需）
                                available_targets = [col for col in column_names if col != source_column]
                                target_columns = st.multiselect(
                                    "关联列 *",
                                    available_targets,
                                    default=[col for col in rel_config["target_columns"] if col in available_targets],
                                    key=f"targets_{selected_table}_{i}",
                                    help="选择与源列相关的其他列（计算、约束、依赖关系必需）"
                                )
                                rel_config["target_columns"] = target_columns
                            else:
                                # 目标列可选
                                available_targets = [col for col in column_names if col != source_column]
                                target_columns = st.multiselect(
                                    "关联列（可选）",
                                    available_targets,
                                    default=[col for col in rel_config["target_columns"] if col in available_targets],
                                    key=f"targets_{selected_table}_{i}",
                                    help="可选择相关列，单表分析通常不需要配置"
                                )
                                rel_config["target_columns"] = target_columns

                                # 如果没有选择目标列，提供说明
                                if not target_columns:
                                    st.info("💡 单表分析：此列作为独立维度，无需配置关联列")

                        with col3:
                            # 删除关系按钮
                            if st.button("🗑️", key=f"del_rel_{selected_table}_{i}", help="删除此关系"):
                                st.session_state[f"relationships_{selected_table}"].pop(i)
                                st.rerun()

                        # 关系类型和描述
                        col1, col2 = st.columns([1, 2])

                        with col1:
                            relationship_type = st.selectbox(
                                "关系类型",
                                relationship_types,
                                index=relationship_types.index(rel_config["relationship_type"]) if rel_config["relationship_type"] in relationship_types else 0,
                                key=f"type_{selected_table}_{i}"
                            )
                            rel_config["relationship_type"] = relationship_type

                        with col2:
                            description = st.text_input(
                                "关系描述",
                                value=rel_config["description"],
                                key=f"desc_{selected_table}_{i}",
                                placeholder="例如：销售额 = 单价 × 销量"
                            )
                            rel_config["description"] = description

                        # 关系预览
                        if source_column and description:
                            if target_columns:
                                target_str = " + ".join(target_columns)
                                preview = f"{source_column} ↔ {target_str}: {description}"
                            else:
                                preview = f"{source_column}: {description}"
                            st.caption(f"📋 预览: {preview}")

                        st.divider()

                    # 从现有关系加载到配置中
                    if st.button("🔄 从现有配置加载", key=f"load_existing_{selected_table}"):
                        loaded_relationships = []
                        for rel_key, rel_desc in table_metadata.relationships.items():
                            # 尝试解析现有关系格式
                            if "_" in rel_key:
                                parts = rel_key.split("_")
                                if len(parts) >= 2:
                                    source = parts[0]
                                    targets = parts[1:]
                                    loaded_relationships.append({
                                        "source_column": source if source in column_names else "",
                                        "target_columns": [t for t in targets if t in column_names],
                                        "relationship_type": "业务关系",
                                        "description": rel_desc
                                    })

                        st.session_state[f"relationships_{selected_table}"] = loaded_relationships
                        st.success("✅ 已加载现有关系配置")
                        st.rerun()

                # 智能建议和模板应用
                with st.expander("💡 智能建议和模板", expanded=False):
                    st.write("**基于列名的智能建议：**")

                    # 分析当前列名，提供智能建议
                    suggestions = MetadataUI._analyze_column_relationships(column_names)

                    if suggestions:
                        for suggestion in suggestions:
                            col1, col2 = st.columns([3, 1])
                            with col1:
                                st.info(f"🔍 发现可能的关系: {suggestion['description']}")
                                st.caption(f"涉及列: {', '.join(suggestion['columns'])}")

                            with col2:
                                if st.button("应用", key=f"apply_suggestion_{selected_table}_{suggestion['key']}"):
                                    # 应用建议到配置中
                                    new_rel = {
                                        "source_column": suggestion['source'],
                                        "target_columns": suggestion['targets'],
                                        "relationship_type": suggestion['type'],
                                        "description": suggestion['description']
                                    }
                                    st.session_state[f"relationships_{selected_table}"].append(new_rel)
                                    st.success("✅ 建议已应用")
                                    st.rerun()
                    else:
                        st.info("暂无智能建议，您可以手动配置关系")

                    st.divider()

                    # 关系模板应用
                    st.write("**关系模板：**")

                    selected_template_type = st.selectbox(
                        "选择模板类型",
                        list(relationship_templates.keys()),
                        key=f"template_type_{selected_table}"
                    )

                    if selected_template_type:
                        templates = relationship_templates[selected_template_type]

                        for template_key, template_desc in templates.items():
                            col1, col2 = st.columns([3, 1])
                            with col1:
                                st.write(f"📋 {template_desc}")
                                st.caption(f"模板: {template_key}")

                            with col2:
                                if st.button("使用", key=f"use_template_{selected_table}_{template_key}"):
                                    # 解析模板并应用
                                    template_parts = template_key.split('_')
                                    if len(template_parts) >= 2:
                                        source = template_parts[0]
                                        targets = template_parts[1:]

                                        # 检查列是否存在
                                        if source in column_names and all(t in column_names for t in targets):
                                            new_rel = {
                                                "source_column": source,
                                                "target_columns": targets,
                                                "relationship_type": selected_template_type,
                                                "description": template_desc
                                            }
                                            st.session_state[f"relationships_{selected_table}"].append(new_rel)
                                            st.success("✅ 模板已应用")
                                            st.rerun()
                                        else:
                                            st.warning("⚠️ 模板中的列名在当前表格中不存在")
                
                # 保存按钮
                if st.button("💾 保存表格元数据", key=f"save_table_{selected_table}"):
                    # 构建新的关系字典
                    new_relationships = {}

                    # 从结构化配置转换为兼容格式
                    relationships_config = st.session_state.get(f"relationships_{selected_table}", [])
                    for rel_config in relationships_config:
                        source = rel_config.get("source_column", "")
                        targets = rel_config.get("target_columns", [])
                        rel_type = rel_config.get("relationship_type", "")
                        description = rel_config.get("description", "")

                        if source and description:  # 只需要源列和描述
                            if targets:
                                # 有关联列：源列_目标列1_目标列2
                                rel_key = f"{source}_{'_'.join(targets)}"
                            else:
                                # 无关联列：仅源列
                                rel_key = source

                            # 组合关系描述：[类型] 描述
                            rel_value = f"[{rel_type}] {description}"
                            new_relationships[rel_key] = rel_value

                    # 更新表格元数据
                    table_metadata.description = new_description
                    table_metadata.business_domain = new_business_domain
                    table_metadata.primary_keys = new_primary_keys
                    table_metadata.relationships = new_relationships
                    table_metadata.updated_at = datetime.now().isoformat()

                    # 保存配置
                    metadata_manager._save_configurations()

                    # 清空会话状态中的临时配置
                    if f"relationships_{selected_table}" in st.session_state:
                        del st.session_state[f"relationships_{selected_table}"]

                    st.success("✅ 表格元数据已保存")
                    st.rerun()
                
                # 验证元数据
                st.subheader("🔍 元数据验证")
                validation_result = metadata_manager.validate_metadata(selected_table)
                
                if validation_result['errors']:
                    for error in validation_result['errors']:
                        st.error(f"❌ {error}")
                
                if validation_result['warnings']:
                    for warning in validation_result['warnings']:
                        st.warning(f"⚠️ {warning}")
                
                if validation_result['suggestions']:
                    for suggestion in validation_result['suggestions']:
                        st.info(f"💡 {suggestion}")
                
                if not validation_result['errors'] and not validation_result['warnings']:
                    st.success("✅ 元数据验证通过")
    
    @staticmethod
    def _render_column_management():
        """渲染列管理界面"""
        st.subheader("📋 列元数据管理")

        # 添加重要性提示和快速指南
        st.info("🎯 **核心功能**: 列管理是元数据系统的核心，占AI理解信息量的78%以上。完善的列描述和业务含义直接决定查询准确性！")

        # 快速配置指南
        with st.expander("📖 快速配置指南", expanded=False):
            st.markdown("""
            **🚀 列管理配置优先级：**
            1. **业务含义** ⭐⭐⭐ - 最重要！告诉AI这个列在业务中的作用
            2. **详细描述** ⭐⭐⭐ - 解释列的具体含义和用途
            3. **标签分类** ⭐⭐ - 帮助AI理解列的类型和属性
            4. **示例值** ⭐⭐ - 提供具体的数据示例
            5. **约束条件** ⭐ - 数据的限制和规则

            **💡 配置技巧：**
            - 业务含义要从业务角度解释，如"用于客户价值分层分析"
            - 描述要具体明确，避免"某某列的数据"这样的泛泛描述
            - 优先配置核心业务列，如金额、数量、时间、标识等
            """)

        # 获取所有表格
        tables = metadata_manager.get_all_tables()

        # 添加配置状态概览
        MetadataUI._render_configuration_overview(tables)
        
        if not tables:
            st.info("暂无已注册的表格。")
            return

        # 检查是否有快速编辑请求
        selected_table = st.session_state.get("selected_table_for_edit")
        if selected_table and selected_table in tables:
            st.success(f"🎯 快速编辑模式：已选择表格 {selected_table}")
            # 清除快速编辑状态
            if "selected_table_for_edit" in st.session_state:
                del st.session_state["selected_table_for_edit"]
        else:
            # 正常选择表格
            selected_table = st.selectbox("选择表格", tables, key="column_table_select")
        
        if selected_table:
            table_metadata = metadata_manager.get_table_metadata(selected_table)
            column_names = list(table_metadata.columns.keys())

            # 显示已编辑列的状态
            MetadataUI._render_edited_columns_status(selected_table, table_metadata)

            # 检查是否有编辑模式切换请求
            switch_to_single_edit = st.session_state.get(f"switch_to_single_{selected_table}", False)
            target_column = st.session_state.get(f"target_column_{selected_table}", None)

            if switch_to_single_edit and target_column:
                # 清除切换标志
                if f"switch_to_single_{selected_table}" in st.session_state:
                    del st.session_state[f"switch_to_single_{selected_table}"]
                if f"target_column_{selected_table}" in st.session_state:
                    del st.session_state[f"target_column_{selected_table}"]

                # 强制使用单列编辑模式
                edit_mode = "单列编辑"
                # 设置目标列
                st.session_state[f"column_select"] = target_column
                st.info(f"🎯 已切换到单列编辑模式，正在编辑列: {target_column}")
            else:
                # 正常的编辑模式选择
                edit_mode = st.radio(
                    "编辑模式",
                    ["单列编辑", "批量查看", "批量编辑"],
                    horizontal=True,
                    key=f"edit_mode_{selected_table}"
                )

            if edit_mode == "单列编辑":
                MetadataUI._render_single_column_edit(selected_table, table_metadata, column_names)
            elif edit_mode == "批量查看":
                MetadataUI._render_batch_view(selected_table, table_metadata)
            else:  # 批量编辑
                MetadataUI._render_batch_edit(selected_table, table_metadata)
            


    @staticmethod
    def _render_template_management():
        """渲染模板管理界面"""
        st.subheader("🔧 列模板管理")

        # 显示现有模板
        st.write("### 📋 现有模板")

        for category, templates in metadata_manager.column_templates.items():
            with st.expander(f"📁 {category} ({len(templates)} 个模板)"):
                for template_name, template_config in templates.items():
                    st.write(f"**{template_name}**")
                    col1, col2 = st.columns([3, 1])

                    with col1:
                        st.write(f"描述: {template_config.get('description', '无')}")
                        st.write(f"业务含义: {template_config.get('business_meaning', '无')}")
                        st.write(f"标签: {', '.join(template_config.get('tags', []))}")

                    with col2:
                        if st.button("🗑️", key=f"delete_template_{category}_{template_name}", help="删除模板"):
                            del metadata_manager.column_templates[category][template_name]
                            if not metadata_manager.column_templates[category]:
                                del metadata_manager.column_templates[category]
                            metadata_manager._save_configurations()
                            st.success(f"已删除模板: {template_name}")
                            st.rerun()

                    st.divider()

        # 添加新模板
        st.write("### ➕ 添加新模板")

        with st.form("add_template_form"):
            col1, col2 = st.columns(2)

            with col1:
                new_category = st.text_input("模板类别", placeholder="例如：销售相关")
                new_template_name = st.text_input("模板名称", placeholder="例如：销售额")
                new_description = st.text_area("描述", placeholder="详细描述这个字段的含义")

            with col2:
                new_business_meaning = st.text_area("业务含义", placeholder="从业务角度解释这个字段的价值")
                new_data_type = st.selectbox("数据类型", ["string", "int", "float", "datetime", "boolean"])
                new_tags_input = st.text_input("标签", placeholder="用逗号分隔，例如：财务,KPI,收入")

            # 约束条件
            st.write("**约束条件**")
            constraint_col1, constraint_col2 = st.columns(2)

            with constraint_col1:
                min_value = st.text_input("最小值", placeholder="数值类型的最小值")
                max_value = st.text_input("最大值", placeholder="数值类型的最大值")

            with constraint_col2:
                unit = st.text_input("单位", placeholder="例如：元、件、%")
                format_pattern = st.text_input("格式", placeholder="例如：YYYY-MM-DD")

            # 关键词
            keywords_input = st.text_input("匹配关键词", placeholder="用逗号分隔，用于自动匹配列名")

            submitted = st.form_submit_button("💾 添加模板")

            if submitted:
                if new_category and new_template_name:
                    # 构建模板配置
                    template_config = {
                        "description": new_description,
                        "business_meaning": new_business_meaning,
                        "data_type": new_data_type,
                        "tags": [tag.strip() for tag in new_tags_input.split(',') if tag.strip()],
                        "constraints": {},
                        "keywords": [kw.strip() for kw in keywords_input.split(',') if kw.strip()]
                    }

                    # 添加约束条件
                    if min_value:
                        try:
                            template_config["constraints"]["min"] = float(min_value)
                        except ValueError:
                            template_config["constraints"]["min"] = min_value

                    if max_value:
                        try:
                            template_config["constraints"]["max"] = float(max_value)
                        except ValueError:
                            template_config["constraints"]["max"] = max_value

                    if unit:
                        template_config["constraints"]["unit"] = unit

                    if format_pattern:
                        template_config["constraints"]["format"] = format_pattern

                    # 添加模板
                    success = metadata_manager.add_column_template(
                        new_category, new_template_name, template_config
                    )

                    if success:
                        st.success(f"✅ 模板已添加: {new_category}.{new_template_name}")
                        st.rerun()
                    else:
                        st.error("❌ 添加模板失败")
                else:
                    st.error("❌ 请填写类别和模板名称")

    @staticmethod
    def _render_import_export():
        """渲染导入导出界面"""
        st.subheader("📤 导入导出")

        # 导出功能
        st.write("### 📤 导出元数据")

        col1, col2 = st.columns(2)

        with col1:
            export_format = st.selectbox("导出格式", ["JSON", "YAML"])
            export_filename = st.text_input("文件名", value="metadata_export")

        with col2:
            if st.button("📥 导出配置"):
                filename = f"{export_filename}.{export_format.lower()}"
                success = metadata_manager.export_metadata(filename, export_format.lower())

                if success:
                    st.success(f"✅ 配置已导出到: {filename}")

                    # 提供下载链接
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            content = f.read()

                        st.download_button(
                            label="💾 下载文件",
                            data=content,
                            file_name=filename,
                            mime="application/json" if export_format == "JSON" else "text/yaml"
                        )
                    except Exception as e:
                        st.error(f"生成下载链接失败: {e}")
                else:
                    st.error("❌ 导出失败")

        st.divider()

        # 导入功能
        st.write("### 📥 导入元数据")

        uploaded_file = st.file_uploader(
            "选择配置文件",
            type=['json', 'yaml', 'yml'],
            help="支持JSON和YAML格式的元数据配置文件"
        )

        if uploaded_file is not None:
            if st.button("📤 导入配置"):
                try:
                    # 保存上传的文件
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w+b', delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        tmp_file_path = tmp_file.name

                    # 导入配置
                    success = metadata_manager.import_metadata(tmp_file_path)

                    if success:
                        st.success("✅ 配置导入成功")
                        st.rerun()
                    else:
                        st.error("❌ 导入失败")

                    # 清理临时文件
                    import os
                    os.unlink(tmp_file_path)

                except Exception as e:
                    st.error(f"❌ 导入失败: {e}")

        st.divider()

        # 备份管理
        st.write("### 💾 备份管理")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 创建备份"):
                from datetime import datetime
                backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                success = metadata_manager.export_metadata(f"metadata_config/backups/{backup_filename}")

                if success:
                    st.success(f"✅ 备份已创建: {backup_filename}")
                else:
                    st.error("❌ 备份创建失败")

        with col2:
            if st.button("🗑️ 清空所有元数据"):
                if st.session_state.get('confirm_clear_metadata'):
                    metadata_manager.tables_metadata.clear()
                    metadata_manager._save_configurations()
                    st.success("✅ 所有元数据已清空")
                    st.session_state.confirm_clear_metadata = False
                    st.rerun()
                else:
                    st.session_state.confirm_clear_metadata = True
                    st.warning("⚠️ 再次点击确认清空所有元数据")

    @staticmethod
    def render_metadata_summary(table_name: str, df: pd.DataFrame):
        """渲染简化的元数据摘要信息"""
        # 检查是否有元数据
        table_metadata = metadata_manager.get_table_metadata(table_name)

        if not table_metadata:
            # 简化显示：只显示一个简洁的按钮，不显示详细说明
            if st.button("🚀 生成元数据", key=f"auto_gen_{table_name}", help="为数据生成智能元数据"):
                with st.spinner("生成中..."):
                    metadata_manager.register_table(table_name, df)
                    st.success("✅ 元数据已生成")
                    st.rerun()
            return

        # 简化显示：只显示最基本的状态信息，移除详细的metrics和expander
        validation_result = metadata_manager.validate_metadata(table_name)

        if validation_result['warnings'] or validation_result['suggestions']:
            # 简化警告显示
            if st.button("⚙️ 优化设置", key=f"optimize_{table_name}", help="优化元数据配置"):
                st.info("可在元数据管理页面进行详细配置")
        else:
            # 简化成功状态显示
            st.caption("✅ 元数据已配置")

    @staticmethod
    def _analyze_column_relationships(column_names: List[str]) -> List[Dict[str, Any]]:
        """
        分析列名并提供关系建议

        Args:
            column_names: 列名列表

        Returns:
            List[Dict]: 关系建议列表
        """
        suggestions = []

        # 常见的计算关系模式
        calculation_patterns = [
            {
                "pattern": ["销售额", "单价", "销量"],
                "type": "计算关系",
                "description": "销售额 = 单价 × 销量"
            },
            {
                "pattern": ["总价", "单价", "数量"],
                "type": "计算关系",
                "description": "总价 = 单价 × 数量"
            },
            {
                "pattern": ["利润", "收入", "成本"],
                "type": "计算关系",
                "description": "利润 = 收入 - 成本"
            },
            {
                "pattern": ["总额", "价格", "数量"],
                "type": "计算关系",
                "description": "总额 = 价格 × 数量"
            }
        ]

        # 分组关系模式
        grouping_patterns = [
            {
                "keywords": ["地区", "区域", "城市"],
                "targets": ["销售额", "销量", "收入", "业绩"],
                "type": "分组关系",
                "description_template": "按{source}统计{target}"
            },
            {
                "keywords": ["产品", "商品", "类别"],
                "targets": ["销售额", "销量", "库存", "价格"],
                "type": "分组关系",
                "description_template": "按{source}分组分析{target}"
            },
            {
                "keywords": ["时间", "日期", "月份", "年份"],
                "targets": ["销售额", "收入", "成本", "利润"],
                "type": "时间关系",
                "description_template": "按{source}维度分析{target}趋势"
            },
            {
                "keywords": ["销售员", "员工", "人员"],
                "targets": ["销售额", "业绩", "客户数"],
                "type": "业务关系",
                "description_template": "{source}的{target}统计"
            }
        ]

        # 单列属性模式（无需关联列）
        single_column_patterns = [
            {
                "keywords": ["地区", "区域", "城市"],
                "type": "层级关系",
                "description": "地理维度，用于区域分析和地域统计"
            },
            {
                "keywords": ["时间", "日期", "月份", "年份"],
                "type": "时间关系",
                "description": "时间维度，用于趋势分析和时序统计"
            },
            {
                "keywords": ["状态", "等级", "级别", "类型"],
                "type": "分类关系",
                "description": "分类维度，用于状态分组和等级分析"
            },
            {
                "keywords": ["编号", "ID", "代码"],
                "type": "标识关系",
                "description": "唯一标识，用于数据关联和查找"
            },
            {
                "keywords": ["名称", "姓名", "标题"],
                "type": "标识关系",
                "description": "名称标识，用于数据展示和识别"
            }
        ]

        # 检查计算关系
        for pattern in calculation_patterns:
            pattern_cols = pattern["pattern"]
            matched_cols = [col for col in pattern_cols if col in column_names]

            if len(matched_cols) >= 2:  # 至少匹配2个列
                source_col = matched_cols[0]  # 第一个作为源列
                target_cols = matched_cols[1:]  # 其余作为目标列

                suggestions.append({
                    "key": f"calc_{source_col}_{'_'.join(target_cols)}",
                    "source": source_col,
                    "targets": target_cols,
                    "columns": matched_cols,
                    "type": pattern["type"],
                    "description": pattern["description"]
                })

        # 检查分组关系
        for pattern in grouping_patterns:
            keywords = pattern["keywords"]
            targets = pattern["targets"]

            # 找到匹配的源列
            source_cols = [col for col in column_names if any(keyword in col for keyword in keywords)]
            # 找到匹配的目标列
            target_cols = [col for col in column_names if any(target in col for target in targets)]

            for source_col in source_cols:
                for target_col in target_cols:
                    if source_col != target_col:
                        description = pattern["description_template"].format(
                            source=source_col,
                            target=target_col
                        )

                        suggestions.append({
                            "key": f"group_{source_col}_{target_col}",
                            "source": source_col,
                            "targets": [target_col],
                            "columns": [source_col, target_col],
                            "type": pattern["type"],
                            "description": description
                        })

        # 检查单列属性
        for pattern in single_column_patterns:
            keywords = pattern["keywords"]

            # 找到匹配的列
            matched_cols = [col for col in column_names if any(keyword in col for keyword in keywords)]

            for col in matched_cols:
                suggestions.append({
                    "key": f"single_{col}",
                    "source": col,
                    "targets": [],  # 单列无需关联列
                    "columns": [col],
                    "type": pattern["type"],
                    "description": pattern["description"]
                })

        # 去重并限制数量
        unique_suggestions = []
        seen_keys = set()

        for suggestion in suggestions:
            if suggestion["key"] not in seen_keys:
                unique_suggestions.append(suggestion)
                seen_keys.add(suggestion["key"])

        return unique_suggestions[:8]  # 增加到8个建议

    @staticmethod
    def _render_configuration_overview(tables: List[str]):
        """渲染配置状态概览"""
        if not tables:
            return

        st.subheader("📊 配置状态概览")

        # 统计所有表格的配置状态
        total_tables = len(tables)
        total_columns = 0
        configured_columns = 0
        well_configured_columns = 0

        for table_name in tables:
            table_metadata = metadata_manager.get_table_metadata(table_name)
            if table_metadata:
                for col_name, col_metadata in table_metadata.columns.items():
                    total_columns += 1

                    # 检查是否已配置（非默认值）
                    is_configured = (
                        col_metadata.business_meaning != "需要进一步定义业务含义" and
                        not col_metadata.description.startswith(f"{col_name}字段") and
                        col_metadata.tags != ["未分类"]
                    )

                    if is_configured:
                        configured_columns += 1

                        # 检查是否配置完善
                        is_well_configured = (
                            len(col_metadata.business_meaning) > 10 and
                            len(col_metadata.description) > 15 and
                            len(col_metadata.tags) >= 2 and
                            len(col_metadata.examples) > 0
                        )

                        if is_well_configured:
                            well_configured_columns += 1

        # 显示总体统计
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总表格数", total_tables)

        with col2:
            st.metric("总列数", total_columns)

        with col3:
            config_rate = (configured_columns / total_columns * 100) if total_columns > 0 else 0
            st.metric("已配置列", f"{configured_columns}", f"{config_rate:.1f}%")

        with col4:
            quality_rate = (well_configured_columns / total_columns * 100) if total_columns > 0 else 0
            st.metric("优质配置", f"{well_configured_columns}", f"{quality_rate:.1f}%")

        # 配置建议
        if config_rate < 80:
            st.warning(f"⚠️ 配置率较低（{config_rate:.1f}%），建议优先完善核心业务列的元数据")
        elif quality_rate < 60:
            st.info(f"💡 配置率良好（{config_rate:.1f}%），建议提升配置质量（当前{quality_rate:.1f}%）")
        else:
            st.success(f"✅ 配置状态优秀！配置率{config_rate:.1f}%，质量{quality_rate:.1f}%")

    @staticmethod
    def _render_edited_columns_status(table_name: str, table_metadata):
        """渲染已编辑列的状态"""
        st.subheader(f"📋 {table_name} 列配置状态")

        # 分析列配置状态
        configured_cols = []
        needs_attention_cols = []
        default_cols = []

        for col_name, col_metadata in table_metadata.columns.items():
            # 检查是否已配置
            is_configured = (
                col_metadata.business_meaning != "需要进一步定义业务含义" and
                not col_metadata.description.startswith(f"{col_name}字段") and
                col_metadata.tags != ["未分类"]
            )

            if is_configured:
                # 检查配置质量
                is_well_configured = (
                    len(col_metadata.business_meaning) > 10 and
                    len(col_metadata.description) > 15 and
                    len(col_metadata.tags) >= 2
                )

                if is_well_configured:
                    configured_cols.append(col_name)
                else:
                    needs_attention_cols.append(col_name)
            else:
                default_cols.append(col_name)

        # 显示状态统计
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("✅ 已完善", len(configured_cols),
                     help="业务含义和描述都已完善的列")

        with col2:
            st.metric("⚠️ 需优化", len(needs_attention_cols),
                     help="已配置但需要进一步完善的列")

        with col3:
            st.metric("❌ 待配置", len(default_cols),
                     help="仍使用默认配置的列")

        # 显示各类别的列
        if configured_cols:
            with st.expander(f"✅ 已完善配置的列 ({len(configured_cols)}个)", expanded=False):
                for col in configured_cols:
                    col_meta = table_metadata.columns[col]
                    st.write(f"**{col}**: {col_meta.business_meaning[:50]}...")

        if needs_attention_cols:
            with st.expander(f"⚠️ 需要优化的列 ({len(needs_attention_cols)}个)", expanded=True):
                for col in needs_attention_cols:
                    col_meta = table_metadata.columns[col]
                    issues = []
                    if len(col_meta.business_meaning) <= 10:
                        issues.append("业务含义过简")
                    if len(col_meta.description) <= 15:
                        issues.append("描述不够详细")
                    if len(col_meta.tags) < 2:
                        issues.append("标签不足")

                    st.write(f"**{col}**: {', '.join(issues)}")

        if default_cols:
            with st.expander(f"❌ 待配置的列 ({len(default_cols)}个)", expanded=True):
                st.write("以下列仍使用默认配置，建议优先完善：")
                for col in default_cols:
                    st.write(f"- **{col}**")

    @staticmethod
    def _render_single_column_edit(table_name: str, table_metadata, column_names: List[str]):
        """渲染单列编辑界面"""
        selected_column = st.selectbox("选择列", column_names, key="column_select")

        if selected_column:
            column_metadata = table_metadata.columns[selected_column]

            # 列基本信息
            col1, col2 = st.columns(2)

            with col1:
                st.metric("列名", column_metadata.name)
                st.metric("数据类型", column_metadata.data_type)

            with col2:
                st.metric("创建时间", column_metadata.created_at[:10] if column_metadata.created_at else "未知")
                st.metric("更新时间", column_metadata.updated_at[:10] if column_metadata.updated_at else "未知")

            # 编辑列元数据
            st.subheader("✏️ 编辑列信息")

            new_display_name = st.text_input(
                "显示名称",
                value=column_metadata.display_name,
                key=f"display_name_{table_name}_{selected_column}"
            )

            new_description = st.text_area(
                "详细描述",
                value=column_metadata.description,
                height=80,
                key=f"col_desc_{table_name}_{selected_column}"
            )

            new_business_meaning = st.text_area(
                "业务含义 ⭐⭐⭐",
                value=column_metadata.business_meaning,
                height=80,
                key=f"business_{table_name}_{selected_column}",
                help="最重要！从业务角度解释这个列的作用和价值"
            )

            # 示例值编辑
            examples_text = ", ".join(column_metadata.examples)
            new_examples_text = st.text_input(
                "示例值（用逗号分隔）",
                value=examples_text,
                key=f"examples_{table_name}_{selected_column}"
            )

            # 标签编辑
            available_tags = ["财务", "销售", "产品", "地理", "时间", "人员", "库存", "市场", "KPI", "维度", "指标"]
            current_tags = column_metadata.tags or []

            new_tags = st.multiselect(
                "标签",
                available_tags,
                default=[tag for tag in current_tags if tag in available_tags],
                key=f"tags_{table_name}_{selected_column}"
            )

            # 自定义标签
            custom_tags_text = ", ".join([tag for tag in current_tags if tag not in available_tags])
            new_custom_tags_text = st.text_input(
                "自定义标签（用逗号分隔）",
                value=custom_tags_text,
                key=f"custom_tags_{table_name}_{selected_column}"
            )

            # 约束条件编辑
            st.subheader("🔒 约束条件")
            import json
            constraints_json = json.dumps(column_metadata.constraints, ensure_ascii=False, indent=2)
            new_constraints_text = st.text_area(
                "约束条件（JSON格式）",
                value=constraints_json,
                height=100,
                key=f"constraints_{table_name}_{selected_column}"
            )

            # 保存按钮
            if st.button("💾 保存列元数据", key=f"save_column_{table_name}_{selected_column}"):
                success, save_info = MetadataUI._save_column_metadata(
                    table_name, selected_column, column_metadata,
                    new_display_name, new_description, new_business_meaning,
                    new_examples_text, new_tags, new_custom_tags_text, new_constraints_text
                )

                if success:
                    # 将保存信息存储到session_state中，避免页面刷新后丢失
                    st.session_state[f"save_result_{table_name}_{selected_column}"] = save_info
                    st.rerun()

            # 显示保存结果（如果存在）
            save_result_key = f"save_result_{table_name}_{selected_column}"
            if save_result_key in st.session_state:
                save_info = st.session_state[save_result_key]

                # 显示保存成功信息
                st.success("✅ 列元数据保存成功！")

                # 显示保存的内容摘要
                with st.expander("📋 已保存的内容摘要", expanded=True):
                    st.write(f"**列名**: {save_info['column_name']}")
                    st.write(f"**显示名称**: {save_info['display_name']}")
                    st.write(f"**描述**: {save_info['description']}")
                    st.write(f"**业务含义**: {save_info['business_meaning']}")
                    if save_info['examples']:
                        st.write(f"**示例值**: {', '.join(save_info['examples'])}")
                    if save_info['tags']:
                        st.write(f"**标签**: {', '.join(save_info['tags'])}")
                    if save_info['constraints']:
                        st.write(f"**约束条件**: {save_info['constraints']}")

                # 配置质量评估
                quality_score = save_info['quality_score']
                if quality_score >= 80:
                    st.success(f"🌟 配置质量优秀 ({quality_score}分)")
                elif quality_score >= 60:
                    st.info(f"👍 配置质量良好 ({quality_score}分)")
                else:
                    st.warning(f"⚠️ 配置质量待提升 ({quality_score}分)，建议完善业务含义和描述")

                # 添加清除按钮
                if st.button("✅ 确认查看", key=f"clear_save_result_{table_name}_{selected_column}"):
                    del st.session_state[save_result_key]
                    st.rerun()

            # 智能建议
            st.subheader("💡 智能建议")
            suggestions = metadata_manager.get_column_suggestions(selected_column)

            if suggestions:
                st.write("基于列名的建议配置：")
                for i, suggestion in enumerate(suggestions[:3]):
                    with st.expander(f"建议 {i+1}: {suggestion['template_name']} (匹配度: {suggestion['score']}%)"):
                        st.json(suggestion['config'])

                        if st.button(f"应用建议 {i+1}", key=f"apply_{table_name}_{selected_column}_{i}"):
                            config = suggestion['config']
                            updates = {
                                'description': config.get('description', column_metadata.description),
                                'business_meaning': config.get('business_meaning', column_metadata.business_meaning),
                                'tags': config.get('tags', column_metadata.tags),
                                'constraints': config.get('constraints', column_metadata.constraints)
                            }

                            if metadata_manager.update_column_metadata(table_name, selected_column, updates):
                                st.success("✅ 建议已应用")
                                st.rerun()
            else:
                st.info("暂无智能建议")

    @staticmethod
    def _save_column_metadata(table_name: str, column_name: str, column_metadata,
                             display_name: str, description: str, business_meaning: str,
                             examples_text: str, tags: List[str], custom_tags_text: str,
                             constraints_text: str):
        """保存列元数据并返回保存信息"""
        try:
            import json

            # 解析示例值
            new_examples = [ex.strip() for ex in examples_text.split(',') if ex.strip()]

            # 合并标签
            custom_tags = [tag.strip() for tag in custom_tags_text.split(',') if tag.strip()]
            final_tags = tags + custom_tags

            # 解析约束条件
            new_constraints = {}
            if constraints_text.strip():
                new_constraints = json.loads(constraints_text)

            # 更新列元数据
            updates = {
                'display_name': display_name,
                'description': description,
                'business_meaning': business_meaning,
                'examples': new_examples,
                'tags': final_tags,
                'constraints': new_constraints
            }

            success = metadata_manager.update_column_metadata(table_name, column_name, updates)

            if success:
                # 计算配置质量
                quality_score = MetadataUI._assess_column_quality(updates)

                # 返回保存信息
                save_info = {
                    'column_name': column_name,
                    'display_name': display_name,
                    'description': description,
                    'business_meaning': business_meaning,
                    'examples': new_examples,
                    'tags': final_tags,
                    'constraints': new_constraints,
                    'quality_score': quality_score
                }

                return True, save_info
            else:
                return False, None

        except json.JSONDecodeError:
            st.error("❌ 约束条件JSON格式错误")
            return False, None
        except Exception as e:
            st.error(f"❌ 保存失败: {e}")
            return False, None

    @staticmethod
    def _assess_column_quality(updates: Dict[str, Any]) -> int:
        """评估列配置质量"""
        score = 0

        # 业务含义质量 (40分)
        business_meaning = updates.get('business_meaning', '')
        if len(business_meaning) > 20:
            score += 40
        elif len(business_meaning) > 10:
            score += 25
        elif len(business_meaning) > 0:
            score += 10

        # 描述质量 (30分)
        description = updates.get('description', '')
        if len(description) > 30:
            score += 30
        elif len(description) > 15:
            score += 20
        elif len(description) > 0:
            score += 10

        # 标签完整性 (20分)
        tags = updates.get('tags', [])
        if len(tags) >= 3:
            score += 20
        elif len(tags) >= 2:
            score += 15
        elif len(tags) >= 1:
            score += 10

        # 示例值 (10分)
        examples = updates.get('examples', [])
        if len(examples) >= 2:
            score += 10
        elif len(examples) >= 1:
            score += 5

        return score

    @staticmethod
    def _render_batch_view(table_name: str, table_metadata):
        """渲染批量查看界面"""
        st.subheader("📊 批量查看列元数据")

        # 过滤选项
        col1, col2 = st.columns(2)

        with col1:
            filter_type = st.selectbox(
                "筛选条件",
                ["全部列", "已配置列", "待配置列", "需优化列"],
                key=f"filter_type_{table_name}"
            )

        with col2:
            sort_by = st.selectbox(
                "排序方式",
                ["列名", "配置质量", "更新时间"],
                key=f"sort_by_{table_name}"
            )

        # 筛选和排序列
        filtered_columns = []

        for col_name, col_metadata in table_metadata.columns.items():
            # 应用筛选
            include = True

            if filter_type == "已配置列":
                include = (col_metadata.business_meaning != "需要进一步定义业务含义" and
                          not col_metadata.description.startswith(f"{col_name}字段"))
            elif filter_type == "待配置列":
                include = (col_metadata.business_meaning == "需要进一步定义业务含义" or
                          col_metadata.description.startswith(f"{col_name}字段"))
            elif filter_type == "需优化列":
                is_configured = (col_metadata.business_meaning != "需要进一步定义业务含义" and
                               not col_metadata.description.startswith(f"{col_name}字段"))
                is_well_configured = (len(col_metadata.business_meaning) > 10 and
                                    len(col_metadata.description) > 15 and
                                    len(col_metadata.tags) >= 2)
                include = is_configured and not is_well_configured

            if include:
                # 计算质量分数
                quality_score = MetadataUI._assess_column_quality({
                    'business_meaning': col_metadata.business_meaning,
                    'description': col_metadata.description,
                    'tags': col_metadata.tags,
                    'examples': col_metadata.examples
                })

                filtered_columns.append({
                    'name': col_name,
                    'metadata': col_metadata,
                    'quality_score': quality_score
                })

        # 排序
        if sort_by == "配置质量":
            filtered_columns.sort(key=lambda x: x['quality_score'], reverse=True)
        elif sort_by == "更新时间":
            filtered_columns.sort(key=lambda x: x['metadata'].updated_at or '', reverse=True)
        else:  # 列名
            filtered_columns.sort(key=lambda x: x['name'])

        # 显示结果
        st.write(f"找到 {len(filtered_columns)} 个符合条件的列")

        for item in filtered_columns:
            col_name = item['name']
            col_metadata = item['metadata']
            quality_score = item['quality_score']

            # 质量指示器
            if quality_score >= 80:
                quality_icon = "🌟"
                quality_color = "green"
            elif quality_score >= 60:
                quality_icon = "👍"
                quality_color = "blue"
            else:
                quality_icon = "⚠️"
                quality_color = "orange"

            with st.expander(f"{quality_icon} {col_name} (质量: {quality_score}分)", expanded=False):
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.write(f"**描述**: {col_metadata.description}")
                    st.write(f"**业务含义**: {col_metadata.business_meaning}")
                    if col_metadata.tags:
                        st.write(f"**标签**: {', '.join(col_metadata.tags)}")
                    if col_metadata.examples:
                        st.write(f"**示例值**: {', '.join(col_metadata.examples[:3])}")

                with col2:
                    st.write(f"**数据类型**: {col_metadata.data_type}")
                    if col_metadata.updated_at:
                        st.write(f"**更新时间**: {col_metadata.updated_at[:10]}")

                    if st.button(f"编辑", key=f"edit_single_{table_name}_{col_name}"):
                        # 设置切换标志，避免直接修改widget关联的session_state
                        st.session_state[f"switch_to_single_{table_name}"] = True
                        st.session_state[f"target_column_{table_name}"] = col_name
                        st.rerun()

    @staticmethod
    def _render_batch_edit(table_name: str, table_metadata):
        """渲染批量编辑界面"""
        st.subheader("⚡ 批量编辑列元数据")

        st.info("💡 批量编辑模式：可以同时为多个列设置相同的标签、约束条件等通用属性")

        # 选择要编辑的列
        column_names = list(table_metadata.columns.keys())
        selected_columns = st.multiselect(
            "选择要批量编辑的列",
            column_names,
            key=f"batch_columns_{table_name}",
            help="选择多个列进行批量操作"
        )

        if not selected_columns:
            st.warning("请先选择要编辑的列")
            return

        st.write(f"已选择 {len(selected_columns)} 个列: {', '.join(selected_columns)}")

        # 批量编辑选项
        edit_options = st.multiselect(
            "选择要批量编辑的属性",
            ["标签", "约束条件", "业务含义前缀", "描述后缀"],
            key=f"batch_options_{table_name}"
        )

        batch_updates = {}

        # 标签批量编辑
        if "标签" in edit_options:
            st.subheader("🏷️ 批量设置标签")

            tag_action = st.radio(
                "标签操作",
                ["添加标签", "替换标签", "移除标签"],
                key=f"tag_action_{table_name}",
                horizontal=True
            )

            available_tags = ["财务", "销售", "产品", "地理", "时间", "人员", "库存", "市场", "KPI", "维度", "指标"]

            if tag_action in ["添加标签", "替换标签"]:
                batch_tags = st.multiselect(
                    f"选择要{tag_action}的标签",
                    available_tags,
                    key=f"batch_tags_{table_name}"
                )

                custom_batch_tags = st.text_input(
                    "自定义标签（用逗号分隔）",
                    key=f"custom_batch_tags_{table_name}"
                )

                if custom_batch_tags:
                    custom_tags = [tag.strip() for tag in custom_batch_tags.split(',') if tag.strip()]
                    batch_tags.extend(custom_tags)

                batch_updates['tags'] = {'action': tag_action, 'tags': batch_tags}

            elif tag_action == "移除标签":
                # 获取所有选中列的现有标签
                all_existing_tags = set()
                for col_name in selected_columns:
                    col_metadata = table_metadata.columns[col_name]
                    all_existing_tags.update(col_metadata.tags or [])

                tags_to_remove = st.multiselect(
                    "选择要移除的标签",
                    list(all_existing_tags),
                    key=f"remove_tags_{table_name}"
                )

                batch_updates['tags'] = {'action': tag_action, 'tags': tags_to_remove}

        # 约束条件批量编辑
        if "约束条件" in edit_options:
            st.subheader("🔒 批量设置约束条件")

            constraint_templates = {
                "数值范围": {"min": 0, "max": 999999},
                "货币单位": {"unit": "元", "min": 0},
                "百分比": {"unit": "%", "min": 0, "max": 100},
                "日期格式": {"format": "YYYY-MM-DD"},
                "文本长度": {"max_length": 100}
            }

            constraint_template = st.selectbox(
                "选择约束模板",
                ["自定义"] + list(constraint_templates.keys()),
                key=f"constraint_template_{table_name}"
            )

            if constraint_template != "自定义":
                batch_constraints = constraint_templates[constraint_template]
                st.json(batch_constraints)
            else:
                constraint_text = st.text_area(
                    "约束条件（JSON格式）",
                    value='{"min": 0}',
                    key=f"batch_constraints_{table_name}"
                )

                try:
                    import json
                    batch_constraints = json.loads(constraint_text)
                except json.JSONDecodeError:
                    st.error("JSON格式错误")
                    batch_constraints = {}

            if batch_constraints:
                batch_updates['constraints'] = batch_constraints

        # 业务含义前缀
        if "业务含义前缀" in edit_options:
            st.subheader("💼 批量添加业务含义前缀")

            meaning_prefix = st.text_input(
                "业务含义前缀",
                placeholder="例如：用于客户分析的",
                key=f"meaning_prefix_{table_name}"
            )

            if meaning_prefix:
                batch_updates['business_meaning_prefix'] = meaning_prefix

        # 描述后缀
        if "描述后缀" in edit_options:
            st.subheader("📝 批量添加描述后缀")

            description_suffix = st.text_input(
                "描述后缀",
                placeholder="例如：，用于业务分析和报表统计",
                key=f"description_suffix_{table_name}"
            )

            if description_suffix:
                batch_updates['description_suffix'] = description_suffix

        # 预览批量更新
        if batch_updates:
            st.subheader("👀 批量更新预览")

            for col_name in selected_columns:
                col_metadata = table_metadata.columns[col_name]

                with st.expander(f"预览: {col_name}", expanded=False):
                    # 显示当前值和预期更新
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write("**当前配置:**")
                        st.write(f"标签: {', '.join(col_metadata.tags or [])}")
                        st.write(f"约束: {col_metadata.constraints}")
                        st.write(f"业务含义: {col_metadata.business_meaning}")
                        st.write(f"描述: {col_metadata.description}")

                    with col2:
                        st.write("**更新后:**")

                        # 预览标签更新
                        if 'tags' in batch_updates:
                            tag_update = batch_updates['tags']
                            current_tags = set(col_metadata.tags or [])

                            if tag_update['action'] == '添加标签':
                                new_tags = current_tags.union(set(tag_update['tags']))
                            elif tag_update['action'] == '替换标签':
                                new_tags = set(tag_update['tags'])
                            else:  # 移除标签
                                new_tags = current_tags - set(tag_update['tags'])

                            st.write(f"标签: {', '.join(new_tags)}")
                        else:
                            st.write(f"标签: {', '.join(col_metadata.tags or [])}")

                        # 预览约束更新
                        if 'constraints' in batch_updates:
                            new_constraints = {**col_metadata.constraints, **batch_updates['constraints']}
                            st.write(f"约束: {new_constraints}")
                        else:
                            st.write(f"约束: {col_metadata.constraints}")

                        # 预览业务含义更新
                        new_meaning = col_metadata.business_meaning
                        if 'business_meaning_prefix' in batch_updates:
                            prefix = batch_updates['business_meaning_prefix']
                            if not new_meaning.startswith(prefix):
                                new_meaning = prefix + new_meaning
                        st.write(f"业务含义: {new_meaning}")

                        # 预览描述更新
                        new_description = col_metadata.description
                        if 'description_suffix' in batch_updates:
                            suffix = batch_updates['description_suffix']
                            if not new_description.endswith(suffix):
                                new_description = new_description + suffix
                        st.write(f"描述: {new_description}")

        # 执行批量更新
        if batch_updates and st.button("💾 执行批量更新", key=f"batch_save_{table_name}"):
            MetadataUI._execute_batch_update(table_name, selected_columns, batch_updates)

    @staticmethod
    def _execute_batch_update(table_name: str, selected_columns: List[str], batch_updates: Dict[str, Any]):
        """执行批量更新"""
        success_count = 0
        error_count = 0

        for col_name in selected_columns:
            try:
                table_metadata = metadata_manager.get_table_metadata(table_name)
                col_metadata = table_metadata.columns[col_name]

                updates = {}

                # 处理标签更新
                if 'tags' in batch_updates:
                    tag_update = batch_updates['tags']
                    current_tags = set(col_metadata.tags or [])

                    if tag_update['action'] == '添加标签':
                        new_tags = list(current_tags.union(set(tag_update['tags'])))
                    elif tag_update['action'] == '替换标签':
                        new_tags = tag_update['tags']
                    else:  # 移除标签
                        new_tags = list(current_tags - set(tag_update['tags']))

                    updates['tags'] = new_tags

                # 处理约束条件更新
                if 'constraints' in batch_updates:
                    new_constraints = {**col_metadata.constraints, **batch_updates['constraints']}
                    updates['constraints'] = new_constraints

                # 处理业务含义前缀
                if 'business_meaning_prefix' in batch_updates:
                    prefix = batch_updates['business_meaning_prefix']
                    current_meaning = col_metadata.business_meaning
                    if not current_meaning.startswith(prefix):
                        updates['business_meaning'] = prefix + current_meaning

                # 处理描述后缀
                if 'description_suffix' in batch_updates:
                    suffix = batch_updates['description_suffix']
                    current_description = col_metadata.description
                    if not current_description.endswith(suffix):
                        updates['description'] = current_description + suffix

                # 执行更新
                if updates:
                    if metadata_manager.update_column_metadata(table_name, col_name, updates):
                        success_count += 1
                    else:
                        error_count += 1

            except Exception as e:
                st.error(f"更新列 {col_name} 时出错: {e}")
                error_count += 1

        # 显示批量更新结果
        if success_count > 0:
            st.success(f"✅ 批量更新完成！成功更新 {success_count} 个列")

            # 显示更新摘要
            with st.expander("📋 批量更新摘要", expanded=True):
                st.write(f"**更新的列**: {', '.join(selected_columns)}")
                st.write(f"**成功数量**: {success_count}")
                if error_count > 0:
                    st.write(f"**失败数量**: {error_count}")

                # 显示更新的内容
                for key, value in batch_updates.items():
                    if key == 'tags':
                        st.write(f"**标签操作**: {value['action']} - {', '.join(value['tags'])}")
                    elif key == 'constraints':
                        st.write(f"**约束条件**: {value}")
                    elif key == 'business_meaning_prefix':
                        st.write(f"**业务含义前缀**: {value}")
                    elif key == 'description_suffix':
                        st.write(f"**描述后缀**: {value}")

            st.rerun()

        if error_count > 0:
            st.error(f"❌ {error_count} 个列更新失败")
