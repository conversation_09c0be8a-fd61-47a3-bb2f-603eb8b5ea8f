"""
Streamlit集成模块 - 负责与Streamlit框架的集成
"""

import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, Tuple
from ..llm.llm_factory import LLMFactory, EnhancedTongyiLLM
from ..utils.config import TongyiConfig, app_config
from ..utils.logger import get_app_logger
from ..utils.validators import validate_dataframe
from ..metadata.metadata_manager import metadata_manager
from ..utils.context_manager import ContextManager


class StreamlitLLMIntegration:
    """
    Streamlit LLM集成类
    
    负责在Streamlit应用中集成LLM功能。
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化Streamlit集成

        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
        self._llm_instance = None

        # 初始化上下文管理器
        self.context_manager = ContextManager(enable_logging)

        # 初始化session state
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化Streamlit session state"""
        if 'llm_initialized' not in st.session_state:
            st.session_state.llm_initialized = False

        if 'current_data' not in st.session_state:
            st.session_state.current_data = None

        if 'analysis_history' not in st.session_state:
            st.session_state.analysis_history = []

        if 'llm_config' not in st.session_state:
            st.session_state.llm_config = None

        if 'auto_init_attempted' not in st.session_state:
            st.session_state.auto_init_attempted = False

    def auto_initialize_llm(self):
        """自动初始化LLM（如果配置可用）"""
        if st.session_state.auto_init_attempted or st.session_state.llm_initialized:
            return

        st.session_state.auto_init_attempted = True

        # 从配置获取默认值
        try:
            import sys
            from pathlib import Path
            sys.path.insert(0, str(Path(__file__).parent.parent.parent))
            from config.app_settings import get_config
            app_config = get_config()
        except ImportError:
            # 如果无法导入配置，使用环境变量
            import os
            api_key = os.getenv('TONGYI_API_KEY', '')
            if not api_key:
                return False

            success, error_msg = self.setup_llm(
                api_key=api_key,
                model=os.getenv('TONGYI_MODEL', 'qwen-plus'),
                temperature=float(os.getenv('TONGYI_TEMPERATURE', '0.1')),
                max_tokens=int(os.getenv('TONGYI_MAX_TOKENS', '2000')),
                enable_chart_fix=os.getenv('ENABLE_CHART_FIX', 'true').lower() == 'true',
                enable_metadata=os.getenv('ENABLE_METADATA', 'true').lower() == 'true'  # 默认启用元数据
            )

            if success:
                if self.logger:
                    self.logger.info("LLM自动初始化成功（使用环境变量）")
                return True
            else:
                if self.logger:
                    self.logger.warning(f"LLM自动初始化失败: {error_msg}")
                return False

        api_key = app_config.llm.tongyi_api_key
        if api_key:
            # 尝试自动初始化
            success, error_msg = self.setup_llm(
                api_key=api_key,
                model=app_config.llm.tongyi_model,
                temperature=app_config.llm.tongyi_temperature,
                max_tokens=app_config.llm.tongyi_max_tokens,
                enable_chart_fix=app_config.llm.enable_chart_fix,
                enable_metadata=app_config.llm.enable_metadata
            )

            if success:
                if self.logger:
                    self.logger.info("LLM自动初始化成功")
                return True
            else:
                if self.logger:
                    self.logger.warning(f"LLM自动初始化失败: {error_msg}")

        return False

    def auto_load_recent_file(self):
        """自动加载最近上传的文件"""
        if st.session_state.current_data is not None:
            return  # 已有数据，不需要自动加载

        try:
            import sys
            from pathlib import Path
            sys.path.insert(0, str(Path(__file__).parent.parent.parent))
            from config.app_settings import get_config
            app_config = get_config()
            uploaded_dir = app_config.data.uploaded_files_dir
            supported_types = app_config.data.supported_file_types
        except ImportError:
            # 如果无法导入配置，使用默认值
            from pathlib import Path
            uploaded_dir = Path(__file__).parent.parent.parent / "uploaded_files"
            supported_types = ('.csv', '.xlsx', '.xls', '.json')

        if not uploaded_dir.exists():
            return

        # 获取最近的文件
        files = []
        for ext in supported_types:
            files.extend(uploaded_dir.glob(f"*{ext}"))

        if not files:
            return

        # 按修改时间排序，获取最新文件
        latest_file = max(files, key=lambda f: f.stat().st_mtime)

        try:
            # 根据文件类型加载数据
            if latest_file.suffix.lower() == '.csv':
                data = pd.read_csv(latest_file)
            elif latest_file.suffix.lower() in ['.xlsx', '.xls']:
                data = pd.read_excel(latest_file)
            elif latest_file.suffix.lower() == '.json':
                data = pd.read_json(latest_file)
            else:
                return

            # 加载数据
            success, error_msg = self.load_data(data, latest_file.name)
            if success:
                if self.logger:
                    self.logger.info(f"自动加载文件成功: {latest_file.name}")
                # 不显示提示信息，静默加载
            else:
                if self.logger:
                    self.logger.warning(f"自动加载文件失败: {error_msg}")

        except Exception as e:
            if self.logger:
                self.logger.error(f"自动加载文件异常: {str(e)}")

    def format_dataframe_as_html(self, df: pd.DataFrame, max_rows: int = 100) -> str:
        """将DataFrame格式化为美观的HTML表格"""
        # 限制显示行数
        display_df = df.head(max_rows) if len(df) > max_rows else df

        # 创建HTML样式
        html_style = """
        <style>
        .custom-table {
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 0.9em;
            font-family: sans-serif;
            min-width: 400px;
            border-radius: 5px 5px 0 0;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
        }
        .custom-table thead tr {
            background-color: #1f77b4;
            color: #ffffff;
            text-align: left;
        }
        .custom-table th,
        .custom-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dddddd;
        }
        .custom-table tbody tr {
            border-bottom: 1px solid #dddddd;
        }
        .custom-table tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }
        .custom-table tbody tr:last-of-type {
            border-bottom: 2px solid #1f77b4;
        }
        .table-info {
            font-size: 0.8em;
            color: #666;
            margin-top: 10px;
        }
        </style>
        """

        # 转换为HTML
        html_table = display_df.to_html(
            classes='custom-table',
            table_id='data-table',
            escape=False,
            index=True
        )

        # 添加表格信息
        info_text = f"<div class='table-info'>显示 {len(display_df)} 行，共 {len(df)} 行数据</div>"

        return html_style + html_table + info_text
    
    def setup_llm(self, api_key: str, model: str = "qwen-plus", **kwargs) -> Tuple[bool, str]:
        """
        设置LLM实例
        
        Args:
            api_key: API密钥
            model: 模型名称
            **kwargs: 其他配置参数
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 创建配置
            config = TongyiConfig(
                api_key=api_key,
                model=model,
                temperature=kwargs.get('temperature', 0.1),
                max_tokens=kwargs.get('max_tokens', 2000),
                enable_chart_fix=kwargs.get('enable_chart_fix', True),
                enable_metadata=kwargs.get('enable_metadata', True),  # 默认启用元数据
                enable_logging=kwargs.get('enable_logging', True)
            )
            
            # 验证配置
            is_valid, error_msg = LLMFactory.validate_config(config)
            if not is_valid:
                return False, error_msg
            
            # 创建LLM实例
            self._llm_instance = LLMFactory.create_tongyi_llm(
                config=config,
                enable_chart_fix=config.enable_chart_fix,
                enable_metadata=config.enable_metadata,
                enable_logging=config.enable_logging
            )
            
            # 保存到session state
            st.session_state.llm_initialized = True
            st.session_state.llm_config = config
            
            if self.logger:
                self.logger.info(f"LLM设置成功 - 模型: {model}")
            
            return True, ""
            
        except Exception as e:
            error_msg = f"LLM设置失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return False, error_msg
    
    def get_llm_instance(self) -> Optional[EnhancedTongyiLLM]:
        """获取LLM实例"""
        return self._llm_instance
    
    def is_llm_ready(self) -> bool:
        """检查LLM是否准备就绪"""
        return st.session_state.llm_initialized and self._llm_instance is not None
    
    def load_data(self, data: pd.DataFrame, data_name: str = "data") -> Tuple[bool, str]:
        """
        加载数据到session state
        
        Args:
            data: 要加载的DataFrame
            data_name: 数据名称
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 验证数据
            is_valid, error_msg, warnings = validate_dataframe(data)
            if not is_valid:
                return False, error_msg
            
            # 显示警告
            for warning in warnings:
                st.warning(warning)
            
            # 保存数据
            st.session_state.current_data = data
            st.session_state.data_name = data_name
            
            if self.logger:
                self.logger.info(f"数据加载成功 - 形状: {data.shape}, 名称: {data_name}")
            
            return True, ""
            
        except Exception as e:
            error_msg = f"数据加载失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return False, error_msg
    
    def analyze_data_with_context(self, instruction: str, use_metadata: bool = False) -> Tuple[bool, str, str]:
        """
        基于上下文的数据分析（新方法）

        Args:
            instruction: 分析指令
            use_metadata: 是否使用元数据

        Returns:
            (是否成功, 生成的代码, 错误信息)
        """
        if not self.is_llm_ready():
            return False, "", "LLM未初始化"

        if st.session_state.current_data is None:
            return False, "", "未加载数据"

        try:
            # 准备数据上下文
            data = st.session_state.current_data
            context = self._prepare_data_context(data)

            # 构建对话上下文
            conversation_context = self.context_manager.build_context_for_llm(instruction)

            # 准备元数据（如果启用）
            metadata = None
            table_name = st.session_state.get('data_name', 'data')

            if use_metadata and self._llm_instance.enable_metadata:
                metadata = self._llm_instance.metadata_processor.extract_dataframe_metadata(
                    data, table_name
                )

            # 使用上下文感知的分析方法
            code = self._llm_instance.analyze_data_with_context(
                instruction=instruction,
                context=context,
                conversation_context=conversation_context,
                metadata=metadata,
                table_name=table_name
            )

            if self.logger:
                self.logger.info(f"上下文感知数据分析完成 - 指令: {instruction[:50]}...")

            return True, code, ""

        except Exception as e:
            error_msg = f"上下文感知数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)

            # 降级到原有方法
            return self.analyze_data(instruction, use_metadata)

    def analyze_data(self, instruction: str, use_metadata: bool = False) -> Tuple[bool, str, str]:
        """
        分析数据
        
        Args:
            instruction: 用户指令
            use_metadata: 是否使用元数据
            
        Returns:
            (是否成功, 生成的代码, 错误信息)
        """
        if not self.is_llm_ready():
            return False, "", "LLM未初始化"
        
        if st.session_state.current_data is None:
            return False, "", "未加载数据"
        
        try:
            # 准备数据上下文
            data = st.session_state.current_data
            context = self._prepare_data_context(data)
            
            # 准备元数据（如果启用）
            metadata = None
            table_name = st.session_state.get('data_name', 'data')

            if use_metadata and self._llm_instance.enable_metadata:
                metadata = self._llm_instance.metadata_processor.extract_dataframe_metadata(
                    data, table_name
                )

            # 分析数据（传递表格名称以获取业务元数据）
            code = self._llm_instance.analyze_data(instruction, context, metadata, table_name)
            
            # 记录分析历史
            analysis_record = {
                'instruction': instruction,
                'code': code,
                'timestamp': pd.Timestamp.now(),
                'use_metadata': use_metadata
            }
            st.session_state.analysis_history.append(analysis_record)
            
            if self.logger:
                self.logger.info(f"数据分析完成 - 指令: {instruction[:50]}...")
            
            return True, code, ""

        except Exception as e:
            error_msg = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return False, "", error_msg

    def add_conversation_round(self, user_message: str, assistant_message: str,
                             code: Optional[str] = None,
                             execution_result: Optional[Dict] = None) -> bool:
        """
        添加对话轮次到上下文管理器

        Args:
            user_message: 用户消息
            assistant_message: 助手回复
            code: 生成的代码
            execution_result: 执行结果

        Returns:
            是否需要生成摘要
        """
        # 详细日志输出 - 添加的对话轮次信息
        if self.logger:
            self.logger.info("=" * 80)
            self.logger.info("💬 添加新的对话轮次:")
            self.logger.info(f"  用户消息: {user_message}")
            self.logger.info(f"  助手回复: {assistant_message[:100]}..." if len(assistant_message) > 100 else f"  助手回复: {assistant_message}")
            if code:
                self.logger.info(f"  生成代码: {code[:200]}..." if len(code) > 200 else f"  生成代码: {code}")
            if execution_result:
                success = execution_result.get('success', False)
                self.logger.info(f"  执行结果: {'成功' if success else '失败'}")
                if not success and execution_result.get('error'):
                    self.logger.info(f"  错误信息: {execution_result['error']}")
            self.logger.info("=" * 80)

        should_summarize = self.context_manager.add_conversation_round(
            user_message=user_message,
            assistant_message=assistant_message,
            code=code,
            execution_result=execution_result
        )

        if self.logger:
            self.logger.info(f"📊 对话轮次添加完成，是否需要生成摘要: {should_summarize}")

        return should_summarize

    def generate_conversation_summary(self):
        """生成对话摘要"""
        if self.logger:
            self.logger.info("🔄 开始生成对话摘要...")

        summary = self.context_manager.generate_summary()

        if summary and self.logger:
            self.logger.info("✅ 对话摘要生成成功")
        elif self.logger:
            self.logger.warning("⚠️ 对话摘要生成失败")

        return summary

    def get_context_stats(self) -> Dict[str, Any]:
        """获取上下文统计信息"""
        return self.context_manager.get_context_stats()

    def cleanup_context(self, keep_recent_rounds: int = 5):
        """清理上下文数据"""
        self.context_manager.cleanup_old_data(keep_recent_rounds)

    def reset_conversation_context(self):
        """重置对话上下文"""
        self.context_manager.reset_context()
        if self.logger:
            self.logger.info("对话上下文已重置")
    
    def execute_code(self, code: str) -> Tuple[bool, str]:
        """
        在Streamlit环境中执行代码
        
        Args:
            code: 要执行的Python代码
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 准备执行环境
            exec_globals = {
                'st': st,
                'pd': pd,
                'df': st.session_state.current_data,
                'data': st.session_state.current_data,
            }
            
            # 导入常用库
            import numpy as np
            import matplotlib.pyplot as plt
            exec_globals.update({
                'np': np,
                'plt': plt,
            })

            # 添加HTML表格显示函数
            def show_dataframe_html(df, title="数据表格", max_rows=100):
                """显示DataFrame为HTML表格"""
                if isinstance(df, pd.DataFrame):
                    st.subheader(title)
                    html_content = self.format_dataframe_as_html(df, max_rows)
                    st.markdown(html_content, unsafe_allow_html=True)
                else:
                    st.write(df)

            exec_globals['show_dataframe_html'] = show_dataframe_html

            # 添加优化的图表显示函数
            def show_chart(fig=None, title="图表", use_container_width=True):
                """优化的图表显示函数"""
                if fig is not None:
                    st.subheader(title)
                    st.pyplot(fig, use_container_width=use_container_width)
                else:
                    # 如果没有传入图表对象，显示当前的matplotlib图表
                    st.subheader(title)
                    st.pyplot(plt.gcf(), use_container_width=use_container_width)

            def show_plotly_chart(fig, title="交互式图表", use_container_width=True):
                """显示Plotly交互式图表"""
                st.subheader(title)
                st.plotly_chart(fig, use_container_width=use_container_width)

            exec_globals.update({
                'show_chart': show_chart,
                'show_plotly_chart': show_plotly_chart
            })
            
            # 执行代码
            exec(code, exec_globals)
            
            if self.logger:
                self.logger.info("代码执行成功")
            
            return True, ""
            
        except Exception as e:
            error_msg = f"代码执行失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return False, error_msg
    
    def _prepare_data_context(self, data: pd.DataFrame) -> str:
        """准备数据上下文信息（安全版本 - 无敏感数据）"""
        context_parts = [
            f"数据形状: {data.shape}",
            f"列名: {list(data.columns)}",
            f"数据类型:\n{data.dtypes.to_string()}",
        ]

        # 添加数据质量摘要（无敏感信息）
        context_parts.append("数据质量摘要:")
        context_parts.append(f"- 总记录数: {len(data)}")
        context_parts.append(f"- 缺失值情况: {'存在缺失值' if data.isnull().any().any() else '无缺失值'}")
        context_parts.append(f"- 重复记录: {'存在重复' if data.duplicated().any() else '无重复'}")

        # 添加列特征分析（无具体数值）
        context_parts.append("列特征分析:")
        for col in data.columns:
            dtype = data[col].dtype
            unique_count = data[col].nunique()
            total_count = len(data)

            if dtype in ['int64', 'float64', 'int32', 'float32']:
                has_variation = data[col].std() > 0 if not data[col].isnull().all() else False
                context_parts.append(f"  - {col}: 数值型列，唯一值{unique_count}个，{'有数据变化' if has_variation else '数据无变化'}，{'适合统计分析' if has_variation else '可能为常量'}")
            else:
                context_parts.append(f"  - {col}: 分类型列，唯一值{unique_count}个，{'存在重复值' if unique_count < total_count else '全部唯一'}，{'适合分组分析' if unique_count < total_count else '适合标识符'}")

        return '\n\n'.join(context_parts)
    
    def display_llm_status(self):
        """显示LLM状态信息"""
        if self.is_llm_ready():
            model_info = self._llm_instance.get_model_info()
            stats = self._llm_instance.get_stats()
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.success("🤖 LLM已就绪")
                st.write(f"**模型**: {model_info['model']}")
                st.write(f"**类型**: {model_info['type']}")
            
            with col2:
                st.write(f"**调用次数**: {stats['call_count']}")
                st.write(f"**总tokens**: {stats['total_tokens']}")
                if stats['call_count'] > 0:
                    st.write(f"**平均响应时间**: {stats['avg_time']:.2f}s")
        else:
            st.error("❌ LLM未初始化")
    
    def display_data_info(self):
        """显示数据信息"""
        if st.session_state.current_data is not None:
            data = st.session_state.current_data
            st.success(f"📊 数据已加载: {st.session_state.get('data_name', 'data')}")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("行数", data.shape[0])
            with col2:
                st.metric("列数", data.shape[1])
            with col3:
                st.metric("缺失值", data.isnull().sum().sum())
        else:
            st.warning("📊 未加载数据")
    
    def get_analysis_history(self) -> list:
        """获取分析历史"""
        return st.session_state.analysis_history
    
    def clear_analysis_history(self):
        """清空分析历史"""
        st.session_state.analysis_history = []
        if self.logger:
            self.logger.info("分析历史已清空")
