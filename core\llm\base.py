"""
Base LLM abstract class for language model integrations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import time


@dataclass
class LLMResponse:
    """LLM响应数据类"""
    content: str
    model: str
    tokens_used: Optional[int] = None
    response_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMRequest:
    """LLM请求数据类"""
    instruction: str
    context: str
    temperature: float = 0.1
    max_tokens: int = 2000
    metadata: Optional[Dict[str, Any]] = None


class BaseLLM(ABC):
    """
    LLM基础抽象类
    
    定义了所有LLM实现必须遵循的接口规范。
    遵循Streamlit最佳实践，避免在主应用中重新定义类。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM实例
        
        Args:
            config: LLM配置字典
        """
        self.config = config
        self._call_count = 0
        self._total_tokens = 0
        self._total_time = 0.0
    
    @property
    @abstractmethod
    def type(self) -> str:
        """返回LLM类型标识"""
        pass
    
    @property
    @abstractmethod
    def model_name(self) -> str:
        """返回模型名称"""
        pass
    
    @abstractmethod
    def _call_api(self, request: LLMRequest) -> LLMResponse:
        """
        调用LLM API的核心实现
        
        Args:
            request: LLM请求对象
            
        Returns:
            LLM响应对象
        """
        pass
    
    def call(self, instruction: str, context: str = "", **kwargs) -> LLMResponse:
        """
        调用LLM生成响应
        
        Args:
            instruction: 指令文本
            context: 上下文信息
            **kwargs: 额外参数
            
        Returns:
            LLM响应对象
        """
        start_time = time.time()
        
        # 创建请求对象
        request = LLMRequest(
            instruction=instruction,
            context=context,
            temperature=kwargs.get('temperature', self.config.get('temperature', 0.1)),
            max_tokens=kwargs.get('max_tokens', self.config.get('max_tokens', 2000)),
            metadata=kwargs.get('metadata')
        )
        
        # 调用API
        response = self._call_api(request)
        
        # 更新统计信息
        response.response_time = time.time() - start_time
        self._call_count += 1
        self._total_time += response.response_time
        if response.tokens_used:
            self._total_tokens += response.tokens_used
        
        return response
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取LLM使用统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'type': self.type,
            'model': self.model_name,
            'call_count': self._call_count,
            'total_tokens': self._total_tokens,
            'total_time': self._total_time,
            'avg_time': self._total_time / max(self._call_count, 1),
            'avg_tokens': self._total_tokens / max(self._call_count, 1),
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self._call_count = 0
        self._total_tokens = 0
        self._total_time = 0.0
    
    @abstractmethod
    def validate_config(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            配置是否有效
        """
        pass
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type}({self.model_name})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"{self.__class__.__name__}(type='{self.type}', model='{self.model_name}')"
