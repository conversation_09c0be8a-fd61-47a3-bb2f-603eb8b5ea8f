"""
Configuration management for the data analysis platform.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import os
from pathlib import Path


@dataclass
class TongyiConfig:
    """通义千问配置类"""
    
    # API配置
    api_key: str
    model: str = "qwen-plus"
    temperature: float = 0.1
    max_tokens: int = 2000
    timeout: int = 30
    
    # 功能开关
    enable_chart_fix: bool = True
    enable_metadata: bool = True  # 默认启用元数据
    enable_logging: bool = True
    
    # 高级配置
    retry_attempts: int = 3
    retry_delay: float = 1.0
    
    @classmethod
    def from_env(cls) -> 'TongyiConfig':
        """从环境变量创建配置"""
        api_key = os.getenv('TONGYI_API_KEY')
        if not api_key:
            raise ValueError("TONGYI_API_KEY environment variable is required")
        
        return cls(
            api_key=api_key,
            model=os.getenv('TONGYI_MODEL', 'qwen-plus'),
            temperature=float(os.getenv('TONGYI_TEMPERATURE', '0.1')),
            max_tokens=int(os.getenv('TONGYI_MAX_TOKENS', '2000')),
            timeout=int(os.getenv('TONGYI_TIMEOUT', '30')),
            enable_chart_fix=os.getenv('ENABLE_CHART_FIX', 'true').lower() == 'true',
            enable_metadata=os.getenv('ENABLE_METADATA', 'true').lower() == 'true',  # 默认启用元数据
            enable_logging=os.getenv('ENABLE_LOGGING', 'true').lower() == 'true',
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model': self.model,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'timeout': self.timeout,
            'enable_chart_fix': self.enable_chart_fix,
            'enable_metadata': self.enable_metadata,
            'enable_logging': self.enable_logging,
            'retry_attempts': self.retry_attempts,
            'retry_delay': self.retry_delay,
        }


@dataclass
class AppConfig:
    """应用程序配置类"""
    
    # 目录配置
    uploaded_files_dir: Path = Path("uploaded_files")
    chat_history_dir: Path = Path("chat_history")
    charts_dir: Path = Path("charts")
    cache_dir: Path = Path("cache")
    
    # Streamlit配置
    page_title: str = "AI数据分析平台"
    page_icon: str = "📊"
    layout: str = "wide"
    
    # 功能配置
    max_file_size_mb: int = 200
    supported_file_types: tuple = ('.csv', '.xlsx', '.xls', '.json', '.txt')
    
    def __post_init__(self):
        """初始化后创建必要目录"""
        for dir_path in [
            self.uploaded_files_dir,
            self.chat_history_dir,
            self.charts_dir,
            self.cache_dir
        ]:
            dir_path.mkdir(exist_ok=True)


# 全局配置实例
app_config = AppConfig()
