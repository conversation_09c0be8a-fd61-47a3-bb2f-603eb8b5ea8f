"""
Core module for the data analysis platform.

This module provides the core functionality for LLM integration,
data processing, and Streamlit integration following best practices.
"""

__version__ = "1.0.0"
__author__ = "Data Analysis Platform Team"

# Core exports
from .llm.llm_factory import LLMFactory
from .utils.config import TongyiConfig
from .utils.context_manager import ContextManager
from .utils.performance_monitor import PerformanceMonitor

__all__ = [
    "LLMFactory",
    "TongyiConfig",
    "ContextManager",
    "PerformanceMonitor",
]
