#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理界面模块 - 适配新架构
为Streamlit应用提供可视化的元数据管理功能
"""

import streamlit as st
import pandas as pd
from .metadata_manager import metadata_manager, ColumnMetadata, TableMetadata
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

class MetadataUI:
    """元数据管理界面类 - 适配新架构"""
    
    @staticmethod
    def render_metadata_management():
        """渲染元数据管理主界面"""
        st.header("🎯 元数据管理")
        st.markdown("管理表格和列的元数据信息，提升AI分析准确性")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📋 列管理", "📊 表格管理", "🔧 模板管理", "📤 导入导出"])

        with tab1:
            MetadataUI._render_column_management()

        with tab2:
            MetadataUI._render_table_management()

        with tab3:
            MetadataUI._render_template_management()

        with tab4:
            MetadataUI._render_import_export()
    
    @staticmethod
    def _render_column_management():
        """渲染列管理界面"""
        st.subheader("📋 列元数据管理")
        
        # 获取所有表格
        tables = metadata_manager.get_all_tables()
        
        if not tables:
            st.info("📝 还没有表格元数据。请先上传数据文件或在「📊 表格管理」中创建表格。")
            return
        
        # 表格选择
        table_names = list(tables.keys())
        selected_table = st.selectbox(
            "选择表格",
            table_names,
            key="column_mgmt_table_select"
        )
        
        if not selected_table:
            return
            
        table_metadata = tables[selected_table]
        
        # 显示表格基本信息
        with st.expander("📊 表格信息", expanded=False):
            st.write(f"**表格名称**: {table_metadata.table_name}")
            st.write(f"**描述**: {table_metadata.description}")
            st.write(f"**业务领域**: {table_metadata.business_domain}")
            st.write(f"**列数量**: {len(table_metadata.columns)}")
        
        # 列管理区域
        st.subheader("📝 列信息管理")
        
        # 如果没有列，提供添加选项
        if not table_metadata.columns:
            st.info("该表格还没有列元数据。")
            
            # 快速添加列的选项
            with st.expander("➕ 快速添加列", expanded=True):
                col_names_input = st.text_area(
                    "输入列名（每行一个）",
                    placeholder="例如:\n用户ID\n用户名\n注册时间\n购买金额",
                    height=100
                )
                
                if st.button("批量添加列") and col_names_input.strip():
                    col_names = [name.strip() for name in col_names_input.strip().split('\n') if name.strip()]
                    for col_name in col_names:
                        metadata_manager.add_column_metadata(
                            table_name=selected_table,
                            column_name=col_name,
                            display_name=col_name,
                            description=f"{col_name}字段",
                            data_type="object",
                            business_meaning=f"表示{col_name}相关信息"
                        )
                    st.success(f"已添加 {len(col_names)} 个列的基础元数据")
                    st.rerun()
        else:
            # 显示现有列
            columns = table_metadata.columns
            
            # 列选择和编辑
            col_names = list(columns.keys())
            selected_column = st.selectbox(
                "选择要编辑的列",
                [""] + col_names,
                key=f"column_select_{selected_table}"
            )
            
            if selected_column:
                MetadataUI._render_column_editor(selected_table, selected_column)
            
            # 添加新列
            with st.expander("➕ 添加新列"):
                MetadataUI._render_add_column_form(selected_table)
            
            # 列列表概览
            with st.expander("📋 所有列概览", expanded=True):
                MetadataUI._render_columns_overview(table_metadata)
    
    @staticmethod
    def _render_column_editor(table_name: str, column_name: str):
        """渲染列编辑器"""
        column_metadata = metadata_manager.get_column_metadata(table_name, column_name)
        if not column_metadata:
            st.error("列元数据不存在")
            return
        
        st.subheader(f"✏️ 编辑列: {column_name}")
        
        with st.form(f"edit_column_{table_name}_{column_name}"):
            # 基本信息
            display_name = st.text_input("显示名称", value=column_metadata.display_name)
            description = st.text_area("详细描述", value=column_metadata.description, height=100)
            
            # 数据类型
            data_types = ["object", "int64", "float64", "bool", "datetime64[ns]", "category"]
            current_type_index = data_types.index(column_metadata.data_type) if column_metadata.data_type in data_types else 0
            data_type = st.selectbox("数据类型", data_types, index=current_type_index)
            
            # 业务含义
            business_meaning = st.text_area("业务含义", value=column_metadata.business_meaning, height=80)
            
            # 示例值
            examples_text = "\n".join(column_metadata.examples)
            examples_input = st.text_area("示例值（每行一个）", value=examples_text, height=60)
            examples = [ex.strip() for ex in examples_input.split('\n') if ex.strip()]
            
            # 标签
            tags_text = ", ".join(column_metadata.tags)
            tags_input = st.text_input("标签（用逗号分隔）", value=tags_text)
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
            
            # 约束条件
            st.subheader("约束条件")
            col1, col2 = st.columns(2)
            
            with col1:
                not_null = st.checkbox("不能为空", value=column_metadata.constraints.get("not_null", False))
                unique = st.checkbox("唯一值", value=column_metadata.constraints.get("unique", False))
            
            with col2:
                min_value = st.number_input("最小值", value=column_metadata.constraints.get("min", 0.0))
                max_value = st.number_input("最大值", value=column_metadata.constraints.get("max", 100.0))
            
            constraints = {
                "not_null": not_null,
                "unique": unique,
                "min": min_value,
                "max": max_value
            }
            
            # 提交按钮
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("💾 保存修改", type="primary"):
                    # 更新列元数据
                    updated_column = ColumnMetadata(
                        name=column_name,
                        display_name=display_name,
                        description=description,
                        data_type=data_type,
                        business_meaning=business_meaning,
                        examples=examples,
                        constraints=constraints,
                        tags=tags,
                        created_at=column_metadata.created_at,
                        updated_at=datetime.now().isoformat()
                    )
                    
                    # 更新到管理器
                    table_metadata = metadata_manager.get_table_metadata(table_name)
                    table_metadata.columns[column_name] = updated_column
                    table_metadata.updated_at = datetime.now().isoformat()
                    metadata_manager._save_tables_metadata()
                    
                    st.success("列元数据已更新！")
                    st.rerun()
            
            with col2:
                if st.form_submit_button("🗑️ 删除列", type="secondary"):
                    if metadata_manager.delete_column_metadata(table_name, column_name):
                        st.success("列元数据已删除！")
                        st.rerun()
                    else:
                        st.error("删除失败")
    
    @staticmethod
    def _render_add_column_form(table_name: str):
        """渲染添加列表单"""
        with st.form(f"add_column_{table_name}"):
            st.subheader("➕ 添加新列")
            
            column_name = st.text_input("列名 *", placeholder="例如: user_id")
            display_name = st.text_input("显示名称", placeholder="例如: 用户ID")
            description = st.text_area("详细描述", placeholder="例如: 用户的唯一标识符", height=80)
            
            data_types = ["object", "int64", "float64", "bool", "datetime64[ns]", "category"]
            data_type = st.selectbox("数据类型", data_types)
            
            business_meaning = st.text_area("业务含义", placeholder="例如: 用于唯一标识系统中的每个用户", height=60)
            
            examples_input = st.text_input("示例值（用逗号分隔）", placeholder="例如: 1001, 1002, 1003")
            examples = [ex.strip() for ex in examples_input.split(',') if ex.strip()]
            
            tags_input = st.text_input("标签（用逗号分隔）", placeholder="例如: 主键, 标识符")
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
            
            if st.form_submit_button("➕ 添加列", type="primary"):
                if column_name:
                    metadata_manager.add_column_metadata(
                        table_name=table_name,
                        column_name=column_name,
                        display_name=display_name or column_name,
                        description=description,
                        data_type=data_type,
                        business_meaning=business_meaning,
                        examples=examples,
                        tags=tags
                    )
                    st.success(f"已添加列: {column_name}")
                    st.rerun()
                else:
                    st.error("请输入列名")
    
    @staticmethod
    def _render_columns_overview(table_metadata: TableMetadata):
        """渲染列概览"""
        if not table_metadata.columns:
            st.info("该表格还没有列元数据")
            return
        
        # 创建概览数据
        overview_data = []
        for col_name, col_metadata in table_metadata.columns.items():
            overview_data.append({
                "列名": col_name,
                "显示名称": col_metadata.display_name,
                "数据类型": col_metadata.data_type,
                "描述": col_metadata.description[:50] + "..." if len(col_metadata.description) > 50 else col_metadata.description,
                "业务含义": col_metadata.business_meaning[:50] + "..." if len(col_metadata.business_meaning) > 50 else col_metadata.business_meaning,
                "标签": ", ".join(col_metadata.tags[:3]) + ("..." if len(col_metadata.tags) > 3 else "")
            })
        
        df = pd.DataFrame(overview_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    
    @staticmethod
    def _render_table_management():
        """渲染表格管理界面"""
        st.subheader("📊 表格元数据管理")
        
        # 获取所有表格
        tables = metadata_manager.get_all_tables()
        
        # 添加新表格
        with st.expander("➕ 创建新表格"):
            with st.form("create_table"):
                table_name = st.text_input("表格名称 *")
                description = st.text_area("表格描述")
                business_domain = st.text_input("业务领域", placeholder="例如: 用户管理, 订单系统, 财务分析")
                
                if st.form_submit_button("创建表格"):
                    if table_name:
                        metadata_manager.create_table_metadata(table_name, description, business_domain)
                        st.success(f"已创建表格: {table_name}")
                        st.rerun()
                    else:
                        st.error("请输入表格名称")
        
        # 显示现有表格
        if tables:
            st.subheader("📋 现有表格")
            
            for table_name, table_metadata in tables.items():
                with st.expander(f"📊 {table_name}", expanded=False):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.write(f"**描述**: {table_metadata.description}")
                        st.write(f"**业务领域**: {table_metadata.business_domain}")
                        st.write(f"**列数量**: {len(table_metadata.columns)}")
                        st.write(f"**创建时间**: {table_metadata.created_at}")
                        st.write(f"**更新时间**: {table_metadata.updated_at}")
                    
                    with col2:
                        if st.button(f"🗑️ 删除", key=f"delete_{table_name}"):
                            if metadata_manager.delete_table_metadata(table_name):
                                st.success("表格已删除")
                                st.rerun()
        else:
            st.info("还没有创建任何表格元数据")
    
    @staticmethod
    def _render_template_management():
        """渲染模板管理界面"""
        st.subheader("🔧 列模板管理")
        st.info("列模板可以帮助您快速创建具有相似特征的列元数据")
        
        # 显示现有模板
        templates = metadata_manager.column_templates
        
        if templates:
            st.subheader("📋 现有模板")
            
            for template_name, template_data in templates.items():
                with st.expander(f"🔧 {template_name}"):
                    st.write(f"**显示名称**: {template_data.get('display_name', '')}")
                    st.write(f"**描述**: {template_data.get('description', '')}")
                    st.write(f"**数据类型**: {template_data.get('data_type', '')}")
                    st.write(f"**业务含义**: {template_data.get('business_meaning', '')}")
                    st.write(f"**标签**: {', '.join(template_data.get('tags', []))}")
        
        # 添加新模板
        with st.expander("➕ 创建新模板"):
            st.info("创建模板功能将在后续版本中提供")
    
    @staticmethod
    def _render_import_export():
        """渲染导入导出界面"""
        st.subheader("📤 导入导出")
        
        # 导出功能
        st.subheader("📤 导出元数据")
        tables = metadata_manager.get_all_tables()
        
        if tables:
            table_names = list(tables.keys())
            selected_table = st.selectbox("选择要导出的表格", table_names)
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("导出为JSON"):
                    export_file = metadata_manager.export_metadata(selected_table, "json")
                    if export_file:
                        st.success(f"已导出到: {export_file}")
            
            with col2:
                if st.button("导出为YAML"):
                    export_file = metadata_manager.export_metadata(selected_table, "yaml")
                    if export_file:
                        st.success(f"已导出到: {export_file}")
        else:
            st.info("没有可导出的表格元数据")
        
        # 导入功能
        st.subheader("📥 导入元数据")
        st.info("导入功能将在后续版本中提供")
    
    @staticmethod
    def render_quick_metadata_setup(table_name: str, dataframe: pd.DataFrame):
        """为新上传的数据快速设置元数据"""
        st.subheader(f"🎯 为 {table_name} 设置元数据")
        
        # 检查是否已有元数据
        existing_metadata = metadata_manager.get_table_metadata(table_name)
        
        if existing_metadata:
            st.success("该表格已有元数据配置")
            if st.button("查看/编辑元数据"):
                st.session_state.show_metadata_ui = True
            return
        
        # 快速设置选项
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🚀 自动创建基础元数据", type="primary"):
                # 创建表格元数据
                metadata_manager.create_table_metadata(
                    table_name=table_name,
                    description=f"从文件 {table_name} 导入的数据表",
                    business_domain="数据分析"
                )
                
                # 为每列创建基础元数据
                for col_name in dataframe.columns:
                    col_dtype = str(dataframe[col_name].dtype)
                    sample_values = dataframe[col_name].dropna().head(3).astype(str).tolist()
                    
                    metadata_manager.add_column_metadata(
                        table_name=table_name,
                        column_name=col_name,
                        display_name=col_name,
                        description=f"{col_name} 字段",
                        data_type=col_dtype,
                        business_meaning=f"表示 {col_name} 相关信息",
                        examples=sample_values
                    )
                
                st.success("基础元数据已创建！")
                st.rerun()
        
        with col2:
            if st.button("📝 手动配置元数据"):
                st.session_state.show_metadata_ui = True
                st.rerun()
