#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据功能测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.metadata.metadata_manager import metadata_manager
import pandas as pd

def test_metadata_functionality():
    """测试元数据功能"""
    print("🧪 开始测试元数据功能...")
    print("=" * 50)
    
    # 1. 测试创建表格元数据
    print("\n1. 测试创建表格元数据")
    table_name = "test_sales_data"
    table_metadata = metadata_manager.create_table_metadata(
        table_name=table_name,
        description="销售数据测试表",
        business_domain="销售分析"
    )
    print(f"✅ 创建表格元数据: {table_name}")
    
    # 2. 测试添加列元数据
    print("\n2. 测试添加列元数据")
    columns_to_add = [
        {
            "column_name": "customer_id",
            "display_name": "客户ID",
            "description": "客户的唯一标识符",
            "data_type": "int64",
            "business_meaning": "用于标识和关联客户信息的主键",
            "examples": ["1001", "1002", "1003"],
            "tags": ["主键", "标识符", "客户"]
        },
        {
            "column_name": "product_name",
            "display_name": "产品名称",
            "description": "销售的产品名称",
            "data_type": "object",
            "business_meaning": "表示销售的具体产品，用于产品分析",
            "examples": ["iPhone 14", "MacBook Pro", "iPad Air"],
            "tags": ["产品", "名称", "文本"]
        },
        {
            "column_name": "sales_amount",
            "display_name": "销售金额",
            "description": "单笔销售的金额",
            "data_type": "float64",
            "business_meaning": "表示销售收入，用于财务分析和业绩统计",
            "examples": ["999.99", "2499.00", "599.00"],
            "tags": ["金额", "收入", "财务"]
        },
        {
            "column_name": "sale_date",
            "display_name": "销售日期",
            "description": "销售发生的日期",
            "data_type": "datetime64[ns]",
            "business_meaning": "记录销售时间，用于时间序列分析和趋势分析",
            "examples": ["2024-01-15", "2024-02-20", "2024-03-10"],
            "tags": ["日期", "时间", "趋势"]
        }
    ]
    
    for col_info in columns_to_add:
        metadata_manager.add_column_metadata(
            table_name=table_name,
            **col_info
        )
        print(f"✅ 添加列元数据: {col_info['column_name']}")
    
    # 3. 测试获取元数据
    print("\n3. 测试获取元数据")
    retrieved_table = metadata_manager.get_table_metadata(table_name)
    if retrieved_table:
        print(f"✅ 获取表格元数据成功")
        print(f"   表格名称: {retrieved_table.table_name}")
        print(f"   描述: {retrieved_table.description}")
        print(f"   列数量: {len(retrieved_table.columns)}")
    
    # 4. 测试生成LLM上下文
    print("\n4. 测试生成LLM上下文")
    llm_context = metadata_manager.generate_llm_context(table_name)
    print("✅ 生成LLM上下文成功")
    print("上下文内容预览:")
    print("-" * 30)
    print(llm_context[:500] + "..." if len(llm_context) > 500 else llm_context)
    print("-" * 30)
    
    # 5. 测试导出功能
    print("\n5. 测试导出功能")
    export_file = metadata_manager.export_metadata(table_name, "json")
    if export_file:
        print(f"✅ 导出元数据成功: {export_file}")
    
    # 6. 测试模板功能
    print("\n6. 测试模板功能")
    templates = metadata_manager.column_templates
    print(f"✅ 可用模板数量: {len(templates)}")
    for template_name in templates.keys():
        print(f"   - {template_name}")
    
    print("\n" + "=" * 50)
    print("🎉 所有元数据功能测试通过！")
    
    return True

def test_metadata_integration():
    """测试元数据与处理器的集成"""
    print("\n🔗 测试元数据与处理器集成...")
    
    try:
        from core.processors.metadata_processor import MetadataProcessor
        
        # 创建处理器实例
        processor = MetadataProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'customer_id': [1001, 1002, 1003],
            'product_name': ['iPhone 14', 'MacBook Pro', 'iPad Air'],
            'sales_amount': [999.99, 2499.00, 599.00],
            'sale_date': pd.to_datetime(['2024-01-15', '2024-02-20', '2024-03-10'])
        })
        
        # 测试基础元数据提取
        basic_metadata = processor.extract_dataframe_metadata(test_data, "test_sales_data")
        print("✅ 基础元数据提取成功")
        
        # 测试增强提示词
        instruction = "分析销售数据，找出销售金额最高的产品"
        context = "测试销售数据"
        
        enhanced_prompt = processor.enhance_prompt(
            instruction=instruction,
            context=context,
            metadata=basic_metadata,
            table_name="test_sales_data"
        )
        
        print("✅ 增强提示词生成成功")
        print("提示词长度:", len(enhanced_prompt))
        
        # 检查是否包含业务元数据
        if "业务元数据" in enhanced_prompt:
            print("✅ 业务元数据已集成到提示词中")
        else:
            print("⚠️ 业务元数据未找到（可能表格不存在）")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始元数据功能完整测试")
    print("=" * 60)
    
    try:
        # 测试基础功能
        basic_test_passed = test_metadata_functionality()
        
        # 测试集成功能
        integration_test_passed = test_metadata_integration()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   基础功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
        print(f"   集成功能测试: {'✅ 通过' if integration_test_passed else '❌ 失败'}")
        
        if basic_test_passed and integration_test_passed:
            print("\n🎉 所有测试通过！元数据功能已成功恢复！")
            print("\n💡 使用建议:")
            print("1. 在Streamlit应用中点击「🎯 元数据管理」按钮")
            print("2. 为您的数据表添加列的业务含义和描述")
            print("3. 在分析时启用「使用元数据增强」选项")
            print("4. 享受更准确的AI数据分析结果！")
            return 0
        else:
            print("\n⚠️ 部分测试失败，请检查错误信息")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
