#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理工具
提供命令行界面来管理项目的元数据，包括清理、刷新、同步等功能
"""

import sys
import argparse
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 元数据管理器加载成功")
except ImportError as e:
    print(f"❌ 无法加载元数据管理器: {e}")
    exit(1)

def show_status():
    """显示当前元数据状态"""
    print("\n📊 当前元数据状态")
    print("=" * 50)

    # 显示存储信息
    storage_info = metadata_manager.get_storage_info()
    print(f"📁 存储目录: {storage_info['config_directory']}")
    print(f"📋 总表格数: {storage_info['total_tables']}")

    # 显示目录结构状态
    print(f"\n📁 目录结构:")
    structure = storage_info['directory_structure']
    for dir_name, exists in structure.items():
        status = "✅" if exists else "❌"
        print(f"  {status} {dir_name}/")

    # 显示配置文件状态
    print(f"\n📄 配置文件:")
    print(f"  {'✅' if storage_info['config_file_exists'] else '❌'} tables_metadata.json")
    print(f"  {'✅' if storage_info['templates_file_exists'] else '❌'} column_templates.json")

    # 显示表格列表
    all_tables = metadata_manager.get_all_tables()
    if all_tables:
        print("\n📋 表格列表:")
        for table in sorted(all_tables):
            metadata = metadata_manager.get_table_metadata(table)
            if metadata:
                print(f"  • {table}")
                print(f"    业务领域: {metadata.business_domain}")
                print(f"    列数: {len(metadata.columns)}")
                print(f"    更新时间: {metadata.updated_at}")
                print()
    else:
        print("\n📋 当前没有注册的表格")

def cleanup_duplicates():
    """清理重复的元数据"""
    print("\n🧹 清理重复元数据")
    print("=" * 50)
    
    result = metadata_manager.cleanup_duplicate_metadata()
    
    print(f"📊 清理结果:")
    print(f"  发现重复项: {result['duplicates_found']}")
    print(f"  删除重复项: {result['duplicates_removed']}")
    
    if result['duplicate_pairs']:
        print(f"\n🔄 重复项处理:")
        for removed, kept in result['duplicate_pairs'].items():
            print(f"  删除: {removed} → 保留: {kept}")
    
    if result['duplicates_removed'] > 0:
        print(f"\n✅ 成功清理 {result['duplicates_removed']} 个重复元数据")
    else:
        print(f"\n✅ 没有发现重复的元数据")

def refresh_metadata(data_dir="uploaded_files"):
    """刷新元数据"""
    print(f"\n🔄 刷新元数据 (目录: {data_dir})")
    print("=" * 50)
    
    result = metadata_manager.refresh_metadata(data_dir)
    
    print(f"📊 刷新结果:")
    print(f"  扫描文件数: {result['scanned_files']}")
    print(f"  清理孤立元数据: {result['orphaned_cleaned']}")
    print(f"  更新表格数: {result['tables_updated']}")
    print(f"  注册新文件数: {result['new_files_registered']}")
    print(f"  刷新后总表格数: {result['total_tables']}")
    
    if result['orphaned_tables']:
        print(f"\n🗑️ 清理的孤立表格:")
        for table in result['orphaned_tables']:
            print(f"  • {table}")
    
    if result['updated_tables']:
        print(f"\n🔄 更新的表格:")
        for table in result['updated_tables']:
            print(f"  • {table}")
    
    if result['new_tables']:
        print(f"\n✨ 新注册的表格:")
        for table in result['new_tables']:
            print(f"  • {table}")

def sync_filesystem(data_dir="uploaded_files", demo_file="demo_data.csv"):
    """与文件系统同步"""
    print(f"\n🔄 与文件系统同步")
    print("=" * 50)
    
    result = metadata_manager.sync_with_filesystem(data_dir, demo_file)
    
    print(f"📊 同步结果:")
    
    # 重复清理结果
    dup_result = result['duplicate_cleanup']
    print(f"  重复清理: {dup_result['duplicates_removed']} 个")
    
    # 目录刷新结果
    dir_result = result['directory_refresh']
    print(f"  目录刷新: 扫描 {dir_result['scanned_files']} 个文件")
    print(f"    - 清理孤立: {dir_result['orphaned_cleaned']} 个")
    print(f"    - 更新表格: {dir_result['tables_updated']} 个")
    print(f"    - 新注册: {dir_result['new_files_registered']} 个")
    
    # 演示数据结果
    demo_result = result['demo_sync']
    if demo_result['demo_registered']:
        print(f"  演示数据: 已注册 {demo_result['table_name']} ({demo_result['records']} 条记录)")
    else:
        print(f"  演示数据: {demo_result.get('message', '未处理')}")
    
    print(f"\n✅ 同步完成，当前总表格数: {result['total_tables_after_sync']}")

def validate_all():
    """验证所有表格的元数据"""
    print("\n🔍 验证所有表格元数据")
    print("=" * 50)
    
    all_tables = metadata_manager.get_all_tables()
    
    if not all_tables:
        print("📋 没有表格需要验证")
        return
    
    total_errors = 0
    total_warnings = 0
    
    for table_name in sorted(all_tables):
        print(f"\n验证表格: {table_name}")
        
        validation_result = metadata_manager.validate_metadata(table_name)
        
        if validation_result['errors']:
            print(f"  ❌ 错误 ({len(validation_result['errors'])}):")
            for error in validation_result['errors']:
                print(f"    • {error}")
            total_errors += len(validation_result['errors'])
        
        if validation_result['warnings']:
            print(f"  ⚠️ 警告 ({len(validation_result['warnings'])}):")
            for warning in validation_result['warnings']:
                print(f"    • {warning}")
            total_warnings += len(validation_result['warnings'])
        
        if validation_result['suggestions']:
            print(f"  💡 建议 ({len(validation_result['suggestions'])}):")
            for suggestion in validation_result['suggestions']:
                print(f"    • {suggestion}")
        
        if not validation_result['errors'] and not validation_result['warnings']:
            print(f"  ✅ 验证通过")
    
    print(f"\n📊 验证总结:")
    print(f"  验证表格数: {len(all_tables)}")
    print(f"  总错误数: {total_errors}")
    print(f"  总警告数: {total_warnings}")

def cleanup_test_tables():
    """清理测试表格"""
    print("\n🧹 清理测试表格")
    print("=" * 50)
    
    result = metadata_manager.cleanup_test_tables()
    
    print(f"📊 清理结果:")
    print(f"  删除表格数: {result['deleted']}")
    print(f"  剩余表格数: {result['remaining']}")
    
    if result['deleted_tables']:
        print(f"\n🗑️ 已删除的测试表格:")
        for table in result['deleted_tables']:
            print(f"  • {table}")
    
    if result['deleted'] > 0:
        print(f"\n✅ 成功清理 {result['deleted']} 个测试表格")
    else:
        print(f"\n✅ 没有发现测试表格")

def create_backup():
    """创建元数据备份"""
    print(f"\n💾 创建元数据备份")
    print("=" * 50)

    try:
        backup_file = metadata_manager.create_backup()
        print(f"✅ 备份已创建: {backup_file}")

        # 显示备份信息
        backup_size = backup_file.stat().st_size
        print(f"📊 备份信息:")
        print(f"  文件大小: {backup_size} 字节")
        print(f"  创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except Exception as e:
        print(f"❌ 创建备份失败: {e}")

def list_backups():
    """列出所有备份"""
    print(f"\n📋 备份文件列表")
    print("=" * 50)

    storage_info = metadata_manager.get_storage_info()
    backups_dir = Path(storage_info['backups_directory'])

    if not backups_dir.exists():
        print("📋 备份目录不存在")
        return

    backup_files = list(backups_dir.glob("*.json"))

    if not backup_files:
        print("📋 没有找到备份文件")
        return

    print(f"📋 找到 {len(backup_files)} 个备份文件:")

    for backup_file in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True):
        file_size = backup_file.stat().st_size
        file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
        print(f"  • {backup_file.name}")
        print(f"    大小: {file_size} 字节")
        print(f"    时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

def export_report(output_file="metadata_report.json"):
    """导出元数据报告"""
    print(f"\n📋 导出元数据报告")
    print("=" * 50)

    all_tables = metadata_manager.get_all_tables()
    storage_info = metadata_manager.get_storage_info()

    report = {
        'export_time': datetime.now().isoformat(),
        'storage_info': storage_info,
        'total_tables': len(all_tables),
        'tables': {}
    }

    for table_name in all_tables:
        metadata = metadata_manager.get_table_metadata(table_name)
        if metadata:
            # 验证元数据
            validation = metadata_manager.validate_metadata(table_name)

            report['tables'][table_name] = {
                'description': metadata.description,
                'business_domain': metadata.business_domain,
                'columns_count': len(metadata.columns),
                'created_at': metadata.created_at,
                'updated_at': metadata.updated_at,
                'version': metadata.version,
                'validation': {
                    'errors': len(validation['errors']),
                    'warnings': len(validation['warnings']),
                    'suggestions': len(validation['suggestions'])
                }
            }

    # 保存报告到exports目录
    exports_dir = Path(storage_info['exports_directory'])
    output_path = exports_dir / output_file

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"✅ 报告已导出: {output_path}")
    print(f"📊 报告内容:")
    print(f"  总表格数: {report['total_tables']}")
    print(f"  导出时间: {report['export_time']}")
    print(f"  存储目录: {storage_info['config_directory']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="元数据管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python metadata_management_tool.py status              # 显示当前状态
  python metadata_management_tool.py cleanup-duplicates  # 清理重复元数据
  python metadata_management_tool.py refresh             # 刷新元数据
  python metadata_management_tool.py sync                # 与文件系统同步
  python metadata_management_tool.py validate            # 验证所有元数据
  python metadata_management_tool.py cleanup-tests       # 清理测试表格
  python metadata_management_tool.py export              # 导出报告
        """
    )
    
    parser.add_argument('command', choices=[
        'status', 'cleanup-duplicates', 'refresh', 'sync',
        'validate', 'cleanup-tests', 'export', 'backup', 'list-backups'
    ], help='要执行的命令')
    
    parser.add_argument('--data-dir', default='uploaded_files',
                       help='数据文件目录 (默认: uploaded_files)')
    
    parser.add_argument('--demo-file', default='demo_data.csv',
                       help='演示数据文件 (默认: demo_data.csv)')
    
    parser.add_argument('--output', default='metadata_report.json',
                       help='报告输出文件 (默认: metadata_report.json)')
    
    args = parser.parse_args()
    
    print("🚀 元数据管理工具")
    print("=" * 60)
    
    try:
        if args.command == 'status':
            show_status()
        elif args.command == 'cleanup-duplicates':
            cleanup_duplicates()
        elif args.command == 'refresh':
            refresh_metadata(args.data_dir)
        elif args.command == 'sync':
            sync_filesystem(args.data_dir, args.demo_file)
        elif args.command == 'validate':
            validate_all()
        elif args.command == 'cleanup-tests':
            cleanup_test_tables()
        elif args.command == 'export':
            export_report(args.output)
        elif args.command == 'backup':
            create_backup()
        elif args.command == 'list-backups':
            list_backups()
        
        print(f"\n🎉 命令 '{args.command}' 执行完成！")
        
    except Exception as e:
        print(f"\n❌ 执行命令时出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
