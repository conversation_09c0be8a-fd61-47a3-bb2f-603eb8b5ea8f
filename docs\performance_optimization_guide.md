# 🚀 性能优化指南

## 📋 概述

本指南提供了AI数据分析平台V2.0的性能优化建议和最佳实践。

## 🎯 性能目标

- **启动时间**: < 3秒
- **LLM响应时间**: < 10秒
- **数据加载时间**: < 2秒
- **图表渲染时间**: < 1秒
- **内存使用**: < 500MB

## 📊 当前性能基线

### 已实现的优化
- ✅ **模块化加载**: 按需导入模块
- ✅ **配置缓存**: 配置对象单例模式
- ✅ **智能重试**: LLM调用失败自动重试
- ✅ **数据清理**: 优化的数据预处理流程

### 性能监控点
- **应用启动**: 配置加载 + 模块导入
- **LLM调用**: API请求 + 响应处理
- **数据处理**: 文件读取 + 数据清理
- **图表生成**: 代码执行 + 渲染显示

## 🔧 优化策略

### 1. 缓存优化

#### 配置缓存
```python
# core/utils/cache.py
from functools import lru_cache
import time

class SmartCache:
    def __init__(self, ttl=3600):
        self._cache = {}
        self._timestamps = {}
        self._ttl = ttl
    
    def get(self, key):
        if key in self._cache:
            if time.time() - self._timestamps[key] < self._ttl:
                return self._cache[key]
            else:
                del self._cache[key]
                del self._timestamps[key]
        return None
    
    def set(self, key, value):
        self._cache[key] = value
        self._timestamps[key] = time.time()

# 使用示例
@lru_cache(maxsize=128)
def get_model_config(model_name):
    return load_model_config(model_name)
```

#### LLM响应缓存
```python
# core/llm/cache_client.py
class CachedTongyiClient(TongyiQianwenClient):
    def __init__(self, config, cache_ttl=1800):
        super().__init__(config)
        self.cache = SmartCache(cache_ttl)
    
    def _call_api(self, request):
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        # 尝试从缓存获取
        cached_response = self.cache.get(cache_key)
        if cached_response:
            return cached_response
        
        # 调用API
        response = super()._call_api(request)
        
        # 缓存响应
        self.cache.set(cache_key, response)
        return response
```

### 2. 异步处理

#### 异步LLM调用
```python
# core/llm/async_client.py
import asyncio
import aiohttp

class AsyncTongyiClient:
    async def call_async(self, instruction, context):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url,
                headers=self.headers,
                json=self.data
            ) as response:
                return await response.json()

# 使用示例
async def analyze_multiple_datasets(datasets):
    tasks = []
    for dataset in datasets:
        task = client.call_async(instruction, dataset)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results
```

#### 后台任务处理
```python
# core/utils/background_tasks.py
import threading
from queue import Queue

class BackgroundTaskManager:
    def __init__(self):
        self.task_queue = Queue()
        self.worker_thread = threading.Thread(target=self._worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def submit_task(self, func, *args, **kwargs):
        self.task_queue.put((func, args, kwargs))
    
    def _worker(self):
        while True:
            func, args, kwargs = self.task_queue.get()
            try:
                func(*args, **kwargs)
            except Exception as e:
                print(f"Background task error: {e}")
            finally:
                self.task_queue.task_done()
```

### 3. 数据处理优化

#### 分块处理大文件
```python
# core/processors/data_loader.py
def load_large_file(file_path, chunk_size=10000):
    """分块加载大文件"""
    chunks = []
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        # 预处理每个块
        processed_chunk = preprocess_chunk(chunk)
        chunks.append(processed_chunk)
    
    # 合并所有块
    return pd.concat(chunks, ignore_index=True)

def preprocess_chunk(chunk):
    """优化的块预处理"""
    # 只处理必要的列
    # 使用向量化操作
    # 避免循环
    return chunk.fillna(0).select_dtypes(include=[np.number])
```

#### 内存优化
```python
# core/utils/memory_optimizer.py
import gc
import psutil

class MemoryOptimizer:
    @staticmethod
    def optimize_dataframe(df):
        """优化DataFrame内存使用"""
        for col in df.columns:
            if df[col].dtype == 'object':
                # 尝试转换为category
                if df[col].nunique() / len(df) < 0.5:
                    df[col] = df[col].astype('category')
            elif df[col].dtype == 'int64':
                # 降低整数精度
                if df[col].min() >= 0 and df[col].max() < 255:
                    df[col] = df[col].astype('uint8')
        return df
    
    @staticmethod
    def cleanup_memory():
        """清理内存"""
        gc.collect()
        return psutil.virtual_memory().percent
```

### 4. Streamlit优化

#### 组件缓存
```python
# app/components/cached_components.py
import streamlit as st

@st.cache_data
def load_and_process_data(file_path):
    """缓存数据加载和处理"""
    data = pd.read_csv(file_path)
    return preprocess_data(data)

@st.cache_resource
def get_llm_instance():
    """缓存LLM实例"""
    return LLMFactory.create_tongyi_llm()

# 使用片段缓存
@st.fragment
def render_chart(data):
    """独立渲染图表组件"""
    st.plotly_chart(create_chart(data))
```

#### 状态管理优化
```python
# app/utils/state_manager.py
class OptimizedStateManager:
    @staticmethod
    def init_state():
        """初始化状态，避免重复创建"""
        if 'data_hash' not in st.session_state:
            st.session_state.data_hash = None
        if 'processed_data' not in st.session_state:
            st.session_state.processed_data = None
    
    @staticmethod
    def update_data(new_data):
        """智能更新数据状态"""
        new_hash = hash(str(new_data))
        if st.session_state.data_hash != new_hash:
            st.session_state.data_hash = new_hash
            st.session_state.processed_data = process_data(new_data)
```

## 📈 性能监控

### 1. 性能指标收集
```python
# core/utils/performance_monitor.py
import time
import functools

def performance_monitor(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # 记录性能指标
        duration = end_time - start_time
        log_performance(func.__name__, duration)
        
        return result
    return wrapper

def log_performance(func_name, duration):
    """记录性能日志"""
    if duration > 1.0:  # 超过1秒的操作
        logger.warning(f"Slow operation: {func_name} took {duration:.2f}s")
    else:
        logger.info(f"Performance: {func_name} took {duration:.2f}s")
```

### 2. 资源使用监控
```python
# core/utils/resource_monitor.py
import psutil
import threading
import time

class ResourceMonitor:
    def __init__(self, interval=5):
        self.interval = interval
        self.monitoring = False
        self.stats = []
    
    def start_monitoring(self):
        self.monitoring = True
        thread = threading.Thread(target=self._monitor_loop)
        thread.daemon = True
        thread.start()
    
    def _monitor_loop(self):
        while self.monitoring:
            stats = {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'timestamp': time.time()
            }
            self.stats.append(stats)
            time.sleep(self.interval)
    
    def get_stats(self):
        return self.stats[-10:]  # 返回最近10次记录
```

## 🎯 具体优化建议

### 立即实施 (高优先级)
1. **启用Streamlit缓存** - 为数据加载和LLM实例添加缓存
2. **优化数据加载** - 实现分块加载和内存优化
3. **添加性能监控** - 监控关键操作的执行时间

### 短期实施 (中优先级)
1. **异步LLM调用** - 实现非阻塞的API调用
2. **智能缓存策略** - 基于内容哈希的缓存机制
3. **后台任务处理** - 将耗时操作移到后台

### 长期实施 (低优先级)
1. **分布式处理** - 支持多进程/多线程处理
2. **数据库缓存** - 使用Redis等外部缓存
3. **CDN加速** - 静态资源CDN加速

## 🧪 性能测试

### 基准测试脚本
```python
# tests/performance/benchmark.py
import time
import statistics

def benchmark_function(func, *args, iterations=10):
    """基准测试函数"""
    times = []
    for _ in range(iterations):
        start = time.time()
        func(*args)
        end = time.time()
        times.append(end - start)
    
    return {
        'mean': statistics.mean(times),
        'median': statistics.median(times),
        'min': min(times),
        'max': max(times),
        'std': statistics.stdev(times) if len(times) > 1 else 0
    }

# 使用示例
def test_llm_performance():
    results = benchmark_function(llm.analyze_data, "test instruction", "test context")
    assert results['mean'] < 10.0  # 平均响应时间小于10秒
```

### 负载测试
```python
# tests/performance/load_test.py
import concurrent.futures
import time

def load_test(func, concurrent_users=10, duration=60):
    """负载测试"""
    start_time = time.time()
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
        futures = []
        
        while time.time() - start_time < duration:
            future = executor.submit(func)
            futures.append(future)
            time.sleep(0.1)  # 控制请求频率
        
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result(timeout=30)
                results.append(result)
            except Exception as e:
                results.append(f"Error: {e}")
    
    return results
```

## 📊 性能优化检查清单

### 代码层面
- [ ] 使用适当的数据结构
- [ ] 避免不必要的循环
- [ ] 使用向量化操作
- [ ] 实现适当的缓存策略

### 系统层面
- [ ] 监控内存使用
- [ ] 优化I/O操作
- [ ] 使用异步处理
- [ ] 实现连接池

### Streamlit层面
- [ ] 使用@st.cache_data和@st.cache_resource
- [ ] 优化组件渲染
- [ ] 减少不必要的重新运行
- [ ] 使用@st.fragment分离组件

## 🎯 预期效果

实施这些优化后，预期能够达到：
- **启动时间减少50%**
- **LLM响应时间减少30%**
- **内存使用减少40%**
- **整体用户体验显著提升**

---

*性能优化是一个持续的过程，建议定期监控和调整优化策略。*
