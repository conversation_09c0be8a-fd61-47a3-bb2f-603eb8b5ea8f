#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问完美集成示例 - 最终版本
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re

load_dotenv()

def convert_print_to_streamlit(code):
    """将print()语句转换为Streamlit组件显示"""
    print("🔄 转换print()为Streamlit组件...")

    lines = code.split('\n')
    converted_lines = []

    for line in lines:
        stripped_line = line.strip()

        # 检查是否是print语句
        if stripped_line.startswith('print(') and stripped_line.endswith(')'):
            # 提取print的内容
            print_content = stripped_line[6:-1]  # 移除 'print(' 和 ')'

            # 判断print的内容类型
            if print_content.startswith('"') or print_content.startswith("'"):
                # 字符串内容，使用st.write
                converted_line = f"st.write({print_content})"
            elif 'grouped_data' in print_content or 'data' in print_content:
                # DataFrame或Series数据，使用st.dataframe
                if print_content in ['grouped_data', 'data']:
                    converted_line = f"st.dataframe({print_content}.to_frame() if hasattr({print_content}, 'to_frame') else {print_content}, use_container_width=True)"
                else:
                    converted_line = f"st.write({print_content})"
            elif 'f"' in print_content or "f'" in print_content:
                # f-string，使用st.success或st.info
                if '答案' in print_content or '最高' in print_content or '最低' in print_content:
                    converted_line = f"st.success({print_content})"
                else:
                    converted_line = f"st.info({print_content})"
            else:
                # 其他内容，使用st.write
                converted_line = f"st.write({print_content})"

            # 保持原有的缩进
            indent = line[:len(line) - len(line.lstrip())]
            converted_lines.append(indent + converted_line)
            print(f"  转换: {stripped_line} → {converted_line}")
        else:
            # 不是print语句，保持原样
            converted_lines.append(line)

    converted_code = '\n'.join(converted_lines)
    print("✅ print()转换完成")
    return converted_code

def apply_deep_chart_fix_standalone(code):
    """独立的深度图表修复函数 - 修复图表消失问题"""
    print("🔧 应用综合图表修复...")

    # 1. 强制转换为Streamlit原生图表
    code = force_streamlit_native_charts(code)

    # 2. 添加强化的数据清理和持久化机制
    if any(chart_method in code for chart_method in ['st.bar_chart', 'st.line_chart', 'st.area_chart', 'st.plotly_chart']):
        data_cleaning_code = """# 强化数据清理（解决Vega-Lite渲染和图表消失问题）
import numpy as np
import pandas as pd
import re

# 深度清理数据中的异常值
if 'df' in locals() or 'df' in globals():
    # 1. 处理数值列中的无穷大值和NaN
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        # 替换无穷大值
        df[col] = df[col].replace([np.inf, -np.inf], np.nan)
        # 填充NaN值
        df[col] = df[col].fillna(0)
        # 确保数据类型正确
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # 处理过大或过小的值（避免Vega-Lite渲染问题）
        if df[col].max() > 1e15:
            df[col] = df[col] / 1e6  # 转换为百万单位
        if abs(df[col].min()) > 1e15:
            df[col] = df[col].clip(lower=-1e12)

    # 2. 清理列名中的特殊字符（避免字段冲突）
    df.columns = [re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(col)) for col in df.columns]

    # 3. 处理重复索引
    if df.index.duplicated().any():
        df = df.reset_index(drop=True)

"""
        return data_cleaning_code + "\n" + code

    return code

def force_streamlit_native_charts(code):
    """强制转换为Streamlit原生图表"""
    print("🔄 强制转换为Streamlit原生图表...")

    # 检测图表类型并替换
    if 'px.pie' in code:
        # 饼图 - 转换为柱状图（因为Streamlit原生不支持饼图）
        print("检测到饼图，转换为安全柱状图")
        return generate_safe_bar_chart()
    elif 'px.bar' in code:
        # Plotly柱状图 - 转换为Streamlit原生柱状图
        print("检测到Plotly柱状图，转换为Streamlit原生柱状图")
        return generate_safe_bar_chart()
    elif 'px.line' in code:
        # Plotly折线图 - 转换为Streamlit原生折线图
        print("检测到Plotly折线图，转换为Streamlit原生折线图")
        return generate_safe_line_chart()
    elif 'st.plotly_chart' in code:
        # 任何使用st.plotly_chart的都转换为安全的柱状图
        print("检测到st.plotly_chart，转换为安全柱状图")
        return generate_safe_bar_chart()
    elif 'st.bar_chart' in code and 'import plotly' in code:
        # 混合使用的情况，强制使用安全版本
        print("检测到混合图表代码，转换为安全柱状图")
        return generate_safe_bar_chart()
    elif 'st.line_chart' in code and 'import plotly' in code:
        # 混合使用的情况，强制使用安全版本
        print("检测到混合图表代码，转换为安全折线图")
        return generate_safe_line_chart()

    return code

def generate_safe_bar_chart():
    """生成安全的柱状图代码 - 修复图表消失问题"""
    return """# 安全的Streamlit原生柱状图（修复版）
import numpy as np
import pandas as pd
import re

# 智能检测产品名称列和销售额列
product_col = None
sales_col = None

# 查找产品名称列（优先级：产品名称 > 产品 > 名称）
for col in df.columns:
    if '产品名称' in str(col) or '产品' in str(col):
        product_col = col
        break

# 查找销售额列（优先级：销售额 > 销售 > 金额）
for col in df.columns:
    if '销售额' in str(col) or '销售' in str(col) or '金额' in str(col):
        sales_col = col
        break

# 如果没找到，使用第一列作为产品列，第二列作为数值列
if product_col is None:
    product_col = df.columns[0]
if sales_col is None:
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    sales_col = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[1]

print(f"使用列: 产品列={product_col}, 销售额列={sales_col}")

# 数据处理
chart_data = df.groupby(product_col)[sales_col].sum().sort_values(ascending=False)

# 强化数据清理（解决Vega-Lite渲染问题）
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引（避免Vega-Lite字段冲突）
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 清理索引名称（避免特殊字符导致的渲染问题）
if hasattr(chart_data.index, 'name') and chart_data.index.name:
    clean_name = re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(chart_data.index.name))
    chart_data.index.name = clean_name

# 显示数据
print("图表数据:")
print(chart_data)
print(f"数据类型: {chart_data.dtype}")
print(f"数据范围: {chart_data.min()} - {chart_data.max()}")

# 使用容器确保图表持久化显示
with st.container():
    # 渲染图表
    if not chart_data.empty and chart_data.sum() > 0:
        st.subheader("📊 产品销售额分析")

        # 使用try-except确保图表渲染稳定
        try:
            st.bar_chart(chart_data, use_container_width=True)

            # 显示统计信息
            st.write("**数据统计:**")
            st.write(f"• 总销售额: ¥{chart_data.sum():,.0f}")
            st.write(f"• 最高销售额: {chart_data.idxmax()} - ¥{chart_data.max():,.0f}")
            st.write(f"• 最低销售额: {chart_data.idxmin()} - ¥{chart_data.min():,.0f}")

            # 显示详细数据
            with st.expander("📋 详细数据"):
                for product_name, sales_value in chart_data.items():
                    percentage = (sales_value / chart_data.sum()) * 100
                    st.write(f"• {product_name}: ¥{sales_value:,.0f} ({percentage:.1f}%)")

        except Exception as chart_error:
            st.error(f"图表渲染失败: {chart_error}")
            # 备用显示方案
            st.dataframe(chart_data.to_frame('销售额'))
    else:
        st.warning("数据无效，无法生成图表")
"""

def generate_safe_line_chart():
    """生成安全的折线图代码 - 修复图表消失问题"""
    return """# 安全的Streamlit原生折线图（修复版）
import numpy as np
import pandas as pd
import re

# 数据处理
if '日期' in df.columns:
    chart_data = df.set_index('日期')['销售额']
else:
    chart_data = df.set_index('产品名称')['销售额']

# 强化数据清理（解决Vega-Lite渲染问题）
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引（避免Vega-Lite字段冲突）
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 清理索引名称（避免特殊字符导致的渲染问题）
if hasattr(chart_data.index, 'name') and chart_data.index.name:
    clean_name = re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(chart_data.index.name))
    chart_data.index.name = clean_name

# 显示数据
print("趋势数据:")
print(chart_data)
print(f"数据类型: {chart_data.dtype}")
print(f"数据范围: {chart_data.min()} - {chart_data.max()}")

# 使用容器确保图表持久化显示
with st.container():
    # 渲染图表
    if not chart_data.empty and chart_data.sum() > 0:
        st.subheader("📈 销售趋势分析")

        # 使用try-except确保图表渲染稳定
        try:
            st.line_chart(chart_data, use_container_width=True)

            # 趋势分析
            if len(chart_data) >= 2:
                change = chart_data.iloc[-1] - chart_data.iloc[0]
                if change > 0:
                    st.success(f"📈 上升趋势: +{change:,.0f}")
                elif change < 0:
                    st.warning(f"📉 下降趋势: {change:,.0f}")
                else:
                    st.info("📊 趋势稳定")

            # 显示详细数据
            with st.expander("📋 详细数据"):
                for index_name, value in chart_data.items():
                    st.write(f"• {index_name}: {value:,.0f}")

        except Exception as chart_error:
            st.error(f"图表渲染失败: {chart_error}")
            # 备用显示方案
            st.dataframe(chart_data.to_frame('销售额'))
    else:
        st.warning("数据无效，无法生成图表")
"""



class TongyiQianwenLLM(LLM):
    # 推荐的模型配置
    RECOMMENDED_MODELS = {
        "qwen-plus": {
            "name": "qwen-plus",
            "description": "通用模型，平衡性能",
            "max_tokens": 4000
        },
        "qwen3-coder-plus": {
            "name": "qwen3-coder-plus",
            "description": "专业代码模型，更高准确率",
            "max_tokens": 8000
        },
        "qwen-max": {
            "name": "qwen-max",
            "description": "最强模型，复杂任务",
            "max_tokens": 6000
        }
    }

    def __init__(self, model="qwen3-coder-plus", max_tokens=None, temperature=0.1):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model

        # 如果没有指定max_tokens，使用推荐配置
        if max_tokens is None:
            model_config = self.RECOMMENDED_MODELS.get(model, self.RECOMMENDED_MODELS["qwen3-coder-plus"])
            self.max_tokens = model_config["max_tokens"]
        else:
            self.max_tokens = max_tokens

        self.temperature = temperature
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

        print(f"🤖 使用模型: {self.model}")
        print(f"📊 max_tokens: {self.max_tokens}")
        if model in self.RECOMMENDED_MODELS:
            print(f"💡 模型说明: {self.RECOMMENDED_MODELS[model]['description']}")
        
    def call(self, instruction, value):
        prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {instruction}

要求:
1. 只返回可执行的Python代码，确保所有代码块都有正确的缩进
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 绝对不要包含创建DataFrame的代码，如pd.read_csv()、pd.read_excel()等
4. 绝对不要包含任何文件读取操作，数据已经在df变量中
5. 不要包含任何中文注释或解释
6. 对于图表生成，必须严格遵循上述显示优先级规则，优先使用Streamlit原生组件或Plotly原生显示
7. 特别注意：for循环、if语句等控制结构必须有正确的缩进，循环体内的代码必须缩进4个空格
5. 对于DataFrame结果显示，优先使用Streamlit组件而不是print()：
   - 对于DataFrame结果：使用 st.dataframe(result_df, use_container_width=True)
   - 对于数值结果：使用 st.metric("指标名", 数值) 或 st.write()
   - 对于"哪个/哪种...最高/最低"类型查询，必须输出：
     a) 所有项目的统计数据：st.dataframe(grouped_data.to_frame(), use_container_width=True)
     b) 明确的答案：st.success(f"答案: 具体项目名称, 具体指标: 具体数值")
   - 对于比较分析，使用st.dataframe()显示完整数据，用st.info()显示结论
   - 只有在无法使用Streamlit组件时才使用print()作为备用方案
6. 🚨 图表生成严格规则（强制执行）：
   - 只有用户明确要求图表、可视化、图形时才生成图表代码
   - 关键词包括：图表、饼图、柱状图、折线图、散点图、可视化、显示图、画图、绘制图、生成图
   - 如果用户只要求数据分析、计算、统计，则只输出数据处理结果，不生成任何图表

   🚨 **绝对禁止使用matplotlib！** 🚨
   - 禁止导入：import matplotlib.pyplot as plt
   - 禁止使用：plt.figure(), plt.bar(), plt.plot(), plt.savefig()等任何matplotlib方法
   - 违反此规则的代码将被自动拒绝

   ✅ **强制使用Streamlit原生图表方法：**
   - 柱状图：st.bar_chart()
   - 折线图：st.line_chart()
   - 散点图：st.scatter_chart()
   - 面积图：st.area_chart()

   - 🚨 对于柱状图，强制使用Streamlit原生方法（禁止使用matplotlib）:
     ```python
     # 使用Streamlit原生柱状图（唯一推荐方法）
     data = df.groupby('分组列')['数值列'].sum().sort_values(ascending=False)
     st.write("**数据分布:**")
     st.dataframe(data.to_frame('数值'), use_container_width=True)

     st.subheader("📊 数据分析结果")
     st.bar_chart(data)

     # 显示具体数值
     st.write("**详细数据:**")
     for item, value in data.items():
         st.write(f"• {item}: {value:,.0f}")
     ```

   - 🚨 对于折线图，强制使用Streamlit原生方法（禁止使用matplotlib）:
     ```python
     # 使用Streamlit原生折线图（唯一推荐方法）
     if '日期' in df.columns:
         df['日期'] = pd.to_datetime(df['日期'])
         data = df.groupby('日期')['数值列'].sum()
     else:
         data = df.groupby('分组列')['数值列'].sum()

     st.write("**趋势数据:**")
     st.dataframe(data.to_frame('数值'), use_container_width=True)

     st.subheader("📈 趋势分析")
     st.line_chart(data)
     ```

   - ⚠️ 重要判断规则（严格执行）：
     * 默认情况下，只进行数据分析和计算，不生成任何图表
     * 只有用户明确使用以下词汇时才生成图表：图表、饼图、柱状图、折线图、散点图、可视化、显示图、画图、绘制图、生成图、图形、chart
     * "分析"、"计算"、"统计"、"总额"、"平均"、"最大"、"最小"等词汇默认不生成图表，只输出数据结果

   - 对于饼图，必须使用Plotly原生显示（最高优先级）:
     ```python
     import plotly.express as px
     data = df.groupby('分组列')['数值列'].sum().reset_index()
     st.write("**数据分布:**")
     st.dataframe(data, use_container_width=True)
     fig = px.pie(data, values='数值列', names='分组列', title='饼图标题')
     fig.update_traces(textposition='inside', textinfo='percent+label')
     fig.update_layout(showlegend=True, legend=dict(orientation="v", yanchor="middle", y=0.5, xanchor="left", x=1.01))
     st.plotly_chart(fig, use_container_width=True)
     ```

   - 重要提醒：对于所有Plotly图表，必须使用st.plotly_chart()显示，绝对不能只写fig作为最后一行！

   - 对于多分组分析（如按地区和产品分析），使用以下模板:
     ```python
     import plotly.express as px
     grouped_data = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()
     print("分组数据:")
     print(grouped_data)
     regions = grouped_data['地区'].unique()
     for region in regions:
         region_data = grouped_data[grouped_data['地区'] == region]
         if not region_data.empty:
             fig = px.pie(region_data, values='销售额', names='产品名称', title=region + '地区各产品销售总额占比')
             fig.update_traces(textposition='inside', textinfo='percent+label')
             st.plotly_chart(fig, use_container_width=True)
     ```
   - 如果Plotly不可用，使用Streamlit原生替代方案:
     ```python
     # 饼图的Streamlit原生替代方案（使用柱状图）
     data = df.groupby('分组列')['数值列'].sum().sort_values(ascending=False)
     print("数据分布:")
     print(data)

     st.subheader("📊 数据分布（柱状图显示）")
     st.bar_chart(data)

     # 显示百分比信息
     total = data.sum()
     st.write("**占比详情:**")
     for item, value in data.items():
         percentage = (value / total) * 100
         st.write(f"• {item}: {value:,.0f} ({percentage:.1f}%)")
     ```
   - 对于柱状图，强制使用Streamlit原生方法:
     ```python
     # 使用Streamlit原生柱状图（唯一推荐方法）
     data = df.groupby('分组列')['数值列'].sum().sort_values(ascending=False)
     print("数据分布:")
     print(data)

     st.subheader("📊 数据分析结果")
     st.bar_chart(data)

     # 显示具体数值
     st.write("**详细数据:**")
     for item, value in data.items():
         st.write(f"• {item}: {value:,.0f}")
     ```

代码:"""
        
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": self.temperature, "max_tokens": self.max_tokens}
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                
                # 清理代码
                code = self.clean_code(code)

                # 额外的代码修复：处理常见的缩进问题
                code = self.fix_common_indentation_issues(code)

                # 强制使用Streamlit原生图表
                code = self.enforce_streamlit_native_charts(code, instruction)

                return code
            else:
                return "print('API调用失败')"
        except Exception as e:
            return f"print('API异常: {e}')"
    
    def clean_code(self, code):
        """清理生成的代码并修复缩进问题"""
        # 移除markdown标记
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)

        # 按行处理
        lines = code.split('\n')
        clean_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 跳过中文解释行
            if self.contains_chinese_explanation(line):
                continue

            # 跳过注释行（但保留代码中的英文注释）
            if line.startswith('#') and any(ord(c) > 127 for c in line):
                continue

            clean_lines.append(line)

        # 使用Python的ast模块来修复缩进
        cleaned_code = self.fix_indentation('\n'.join(clean_lines))

        # 应用深度图表修复
        cleaned_code = self.apply_deep_chart_fix(cleaned_code)

        return cleaned_code

    def fix_indentation(self, code):
        """修复Python代码的缩进 - 简化但可靠的版本"""
        lines = code.split('\n')
        fixed_lines = []
        indent_level = 0

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 检查是否需要减少缩进（else, elif, except, finally）
            if line.startswith(('else:', 'elif ', 'except:', 'finally:')):
                indent_level = max(0, indent_level - 1)
                fixed_lines.append('    ' * indent_level + line)
                indent_level += 1
                continue

            # 检查是否是控制结构开始（以冒号结尾）
            if line.endswith(':'):
                fixed_lines.append('    ' * indent_level + line)
                indent_level += 1
                continue

            # 检查是否应该退出当前缩进块
            if indent_level > 0:
                # 检查当前行是否是顶级语句
                is_top_level_statement = (
                    line.startswith(('import ', 'from ', 'def ', 'class ')) or
                    line.startswith(('plt.figure', 'plt.title', 'plt.xlabel', 'plt.ylabel',
                                   'plt.xticks', 'plt.grid', 'plt.tight_layout', 'plt.show')) or
                    (line.startswith(('print(', 'df', 'data', 'fig', 'max_', 'min_')) and
                     not line.startswith('ax.'))
                )

                # 如果是顶级语句，退出缩进
                if is_top_level_statement:
                    # 检查前一行是否可能需要这行作为缩进内容
                    prev_line = fixed_lines[-1].strip() if fixed_lines else ""
                    if prev_line.startswith('for ') and prev_line.endswith(':'):
                        # 前一行是for循环，当前行应该缩进
                        fixed_lines.append('    ' * indent_level + line)
                    else:
                        # 退出缩进块
                        indent_level = 0
                        fixed_lines.append(line)
                else:
                    # 在缩进块内
                    fixed_lines.append('    ' * indent_level + line)
            else:
                # 顶级语句
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def fix_common_indentation_issues(self, code):
        """修复常见的缩进问题"""
        lines = code.split('\n')
        fixed_lines = []

        for i, line in enumerate(lines):
            if not line.strip():
                continue

            line_stripped = line.strip()

            # 检查前一行是否是需要缩进的控制结构
            prev_line = lines[i-1].strip() if i > 0 else ""
            needs_indent = False

            if prev_line.endswith(':'):
                # 前一行是控制结构（for, if, while, try等）
                if prev_line.startswith(('for ', 'if ', 'while ', 'try:', 'with ', 'def ', 'class ')):
                    needs_indent = True

            # 特殊处理：某些语句应该在控制结构内缩进
            should_be_indented = (
                line_stripped.startswith(('ax.text', 'ax.', 'fig.', 'return ', 'break', 'continue', 'pass')) or
                (line_stripped.startswith(('print(', 'st.')) and prev_line.startswith('for '))
            )

            if needs_indent or should_be_indented:
                # 确保有正确的缩进
                if not line.startswith('    '):
                    fixed_lines.append('    ' + line_stripped)
                else:
                    fixed_lines.append(line)
            else:
                # 保持原有格式
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def enforce_streamlit_native_charts(self, code, instruction):
        """强制使用Streamlit原生图表方法"""
        print(f"🔍 检查图表转换 - 指令: {instruction}")
        print(f"🔍 代码包含matplotlib: {'import matplotlib.pyplot as plt' in code}")

        instruction_lower = instruction.lower()

        # 检查是否需要图表
        chart_keywords = ['柱状图', '条形图', '折线图', '散点图', '面积图', 'bar', 'line', 'scatter', 'area']
        needs_chart = any(keyword in instruction_lower for keyword in chart_keywords)

        print(f"🔍 需要图表: {needs_chart}")

        if not needs_chart:
            return code

        # 检查是否使用了matplotlib而不是Streamlit原生
        has_matplotlib = 'import matplotlib.pyplot as plt' in code
        has_streamlit_chart = 'st.bar_chart' in code or 'st.line_chart' in code

        print(f"🔍 有matplotlib: {has_matplotlib}, 有Streamlit图表: {has_streamlit_chart}")

        if has_matplotlib and not has_streamlit_chart:
            print("🔄 检测到matplotlib代码，转换为Streamlit原生图表")

            # 根据查询类型生成对应的Streamlit原生代码
            if any(keyword in instruction_lower for keyword in ['柱状图', '条形图', 'bar']):
                print("📊 生成Streamlit柱状图")
                return self._generate_streamlit_bar_chart(code)
            elif any(keyword in instruction_lower for keyword in ['折线图', 'line']):
                print("📈 生成Streamlit折线图")
                return self._generate_streamlit_line_chart(code)
            elif any(keyword in instruction_lower for keyword in ['散点图', 'scatter']):
                print("📊 生成Streamlit散点图")
                return self._generate_streamlit_scatter_chart(code)

        print("🔍 不需要转换，返回原代码")
        return code

    def _generate_streamlit_bar_chart(self, original_code):
        """生成Streamlit原生柱状图代码"""
        return """# 使用Streamlit原生柱状图
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print("各产品销售额:")
print(product_sales)

# 使用Streamlit原生柱状图显示
st.subheader("📊 各产品销售额对比")
st.bar_chart(product_sales)

# 显示具体数值
st.write("**详细数据:**")
for product, sales in product_sales.items():
    st.write(f"• {product}: ¥{sales:,.0f}")
"""

    def _generate_streamlit_line_chart(self, original_code):
        """生成Streamlit原生折线图代码"""
        return """# 使用Streamlit原生折线图
if '日期' in df.columns:
    df['日期'] = pd.to_datetime(df['日期'])
    daily_sales = df.groupby('日期')['销售额'].sum()
    print("每日销售额:")
    print(daily_sales)

    st.subheader("📈 销售额趋势")
    st.line_chart(daily_sales)
else:
    # 如果没有日期列，按产品显示趋势
    product_sales = df.groupby('产品名称')['销售额'].sum()
    print("各产品销售额:")
    print(product_sales)

    st.subheader("📈 产品销售额趋势")
    st.line_chart(product_sales)
"""

    def _generate_streamlit_scatter_chart(self, original_code):
        """生成Streamlit原生散点图代码"""
        return """# 使用Streamlit原生散点图
if '价格' in df.columns and '销量' in df.columns:
    scatter_data = df[['价格', '销量']].copy()
    print("价格与销量关系:")
    print(scatter_data.head())

    st.subheader("📊 价格与销量关系")
    st.scatter_chart(scatter_data.set_index('价格')['销量'])
else:
    # 备用方案：销售额与销量
    if '销售额' in df.columns and '销量' in df.columns:
        scatter_data = df[['销售额', '销量']].copy()
        print("销售额与销量关系:")
        print(scatter_data.head())

        st.subheader("📊 销售额与销量关系")
        st.scatter_chart(scatter_data.set_index('销售额')['销量'])
"""

    def apply_deep_chart_fix(self, code):
        """应用深度图表修复"""
        # 强制替换所有图表代码为深度修复版本
        if 'st.bar_chart' in code:
            print("🔧 强制应用深度柱状图修复...")
            return self._generate_deep_fix_bar_chart()
        elif 'st.line_chart' in code:
            print("🔧 强制应用深度折线图修复...")
            return self._generate_deep_fix_line_chart()
        elif 'st.plotly_chart' in code:
            print("🔧 强制应用深度图表修复...")
            return self._generate_deep_fix_bar_chart()  # 转换为安全的柱状图

        return code

    def _generate_deep_fix_bar_chart(self):
        """生成深度修复的柱状图代码 - 解决图表消失问题"""
        return """# 使用安全的Streamlit图表渲染（深度修复版 - 解决图表消失问题）
import numpy as np
import pandas as pd
import re

# 智能检测产品名称列和销售额列
product_col = None
sales_col = None

# 查找产品名称列（优先级：产品名称 > 产品 > 名称）
for col in df.columns:
    if '产品名称' in str(col) or '产品' in str(col):
        product_col = col
        break

# 查找销售额列（优先级：销售额 > 销售 > 金额）
for col in df.columns:
    if '销售额' in str(col) or '销售' in str(col) or '金额' in str(col):
        sales_col = col
        break

# 如果没找到，使用第一列作为产品列，第二列作为数值列
if product_col is None:
    product_col = df.columns[0]
if sales_col is None:
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    sales_col = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[1]

print(f"使用列: 产品列={product_col}, 销售额列={sales_col}")

# 数据处理和清理
chart_data = df.groupby(product_col)[sales_col].sum()

# 深度数据清理（解决Vega-Lite渲染问题）
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引（避免Vega-Lite字段冲突）
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 清理索引名称（避免特殊字符导致的渲染问题）
if hasattr(chart_data.index, 'name') and chart_data.index.name:
    clean_name = re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(chart_data.index.name))
    chart_data.index.name = clean_name

# 处理过大或过小的值（避免Vega-Lite scale binding问题）
if chart_data.max() > 1e15:
    chart_data = chart_data / 1e6  # 转换为百万单位
    value_unit = "（百万）"
else:
    value_unit = ""

# 排序数据
chart_data = chart_data.sort_values(ascending=False)

# 验证数据质量
print("图表数据:")
print(chart_data)
print(f"数据类型: {chart_data.dtype}")
print(f"数据范围: {chart_data.min()} - {chart_data.max()}")

# 使用容器和错误处理确保图表持久化显示
with st.container():
    # 安全渲染图表
    if not chart_data.empty and chart_data.sum() != 0:
        st.subheader(f"📊 各产品销售额对比{value_unit}")

        try:
            # 使用Streamlit原生图表，避免Vega-Lite问题
            st.bar_chart(chart_data, use_container_width=True)

            # 显示统计摘要
            st.write("**数据统计:**")
            st.write(f"• 总销售额: ¥{chart_data.sum():,.0f}{value_unit}")
            st.write(f"• 最高销售额: {chart_data.idxmax()} - ¥{chart_data.max():,.0f}{value_unit}")
            st.write(f"• 最低销售额: {chart_data.idxmin()} - ¥{chart_data.min():,.0f}{value_unit}")

            # 显示详细数据（可展开）
            with st.expander("📋 详细数据"):
                for product_name, sales_amount in chart_data.items():
                    percentage = (sales_amount / chart_data.sum()) * 100
                    st.write(f"• {product_name}: ¥{sales_amount:,.0f}{value_unit} ({percentage:.1f}%)")

        except Exception as render_error:
            st.error(f"图表渲染失败: {render_error}")
            # 备用显示方案
            st.write("**数据表格（备用显示）:**")
            st.dataframe(chart_data.to_frame(f'销售额{value_unit}'))
    else:
        st.warning("📊 数据无法生成图表")
        # 显示原始数据用于调试
        if not chart_data.empty:
            st.write("**原始数据（调试用）:**")
            st.dataframe(chart_data.to_frame('销售额'))
"""

    def contains_chinese_explanation(self, line):
        """检查是否包含中文解释"""
        # 检查是否包含中文标点符号
        chinese_punctuation = '。，：；！？""''（）【】'
        if any(p in line for p in chinese_punctuation):
            return True
            
        # 检查是否以中文开头且不是代码
        if line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print']):
            return True
            
        return False
    
    @property
    def type(self):
        return "tongyi_qianwen"

def _should_generate_chart(query):
    """判断用户查询是否需要生成图表"""
    # 明确的图表关键词
    chart_keywords = [
        '图表', '饼图', '柱状图', '折线图', '散点图', '可视化', '显示图',
        '画图', '绘制图', '生成图', '图形', 'chart', '可视', '展示图'
    ]

    # 检查是否包含图表关键词
    query_lower = query.lower()
    for keyword in chart_keywords:
        if keyword in query_lower:
            return True

    # 如果只是分析、计算、统计类查询，不生成图表
    analysis_only_keywords = ['分析', '计算', '统计', '总额', '平均', '最大', '最小', '求和']
    chart_keywords_in_query = any(keyword in query_lower for keyword in chart_keywords)

    if not chart_keywords_in_query:
        print(f"🎯 检测到纯分析查询，不生成图表: {query}")
        return False

    return True

def analyze_data(df, query, table_name="data_table", use_metadata=True):
    """分析数据并返回结果，支持元数据增强"""
    import io
    import sys
    from contextlib import redirect_stdout, redirect_stderr

    # 预先判断是否需要生成图表
    should_chart = _should_generate_chart(query)

    # 创建结果字典
    result = {
        'query': query,
        'table_name': table_name,
        'data_shape': df.shape,
        'code': '',
        'output': '',
        'error': None,
        'success': False,
        'chart_path': None,
        'chart_figure': None,
        'has_chart': False,
        'metadata_used': use_metadata
    }

    print(f"🔍 查询: {query}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 表格名称: {table_name}")

    try:
        # 根据是否使用元数据选择LLM
        if use_metadata:
            try:
                from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM
                llm = EnhancedTongyiQianwenLLM()
                llm.set_current_data(table_name, df)
                print("🎯 使用增强版LLM（包含元数据支持）")
            except ImportError:
                print("⚠️ 增强版LLM不可用，使用标准版本")
                llm = TongyiQianwenLLM()
        else:
            llm = TongyiQianwenLLM()
            print("📝 使用标准版LLM")

        code = llm.call(query, df.to_string())
        result['code'] = code

        print(f"📝 生成的代码:")
        print(code)
        print(f"🚀 执行结果:")

        # 捕获执行输出
        output_buffer = io.StringIO()
        error_buffer = io.StringIO()

        # 创建简化的执行环境，专注于Streamlit原生图表
        from datetime import datetime
        import os
        import warnings

        # 抑制所有警告，专注于核心功能
        warnings.filterwarnings('ignore')

        # 简化的字体设置（专注于Streamlit原生图表）
        def setup_chinese_font():
            """简化的字体设置"""
            print(f"✅ 中文字体设置成功")
            return True

        # 调用字体设置并获取结果
        chinese_font_available = setup_chinese_font()

        # 确保charts目录存在
        charts_dir = 'charts'
        os.makedirs(charts_dir, exist_ok=True)

        # 生成唯一的图表文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"chart_{timestamp}.png"
        chart_path = os.path.join(charts_dir, chart_filename)

        # 简化的图表处理（专注于Streamlit原生图表）
        def save_chart_wrapper():
            """简化的图表包装函数"""
            print(f"📊 使用Streamlit原生图表，无需保存文件")
            return None

        # 用于存储图表对象的变量
        chart_figure = None

        # 创建DataFrame副本，避免修改原始数据
        df_copy = df.copy()

        # 导入Streamlit和Plotly（如果可用）
        try:
            import streamlit as st
            streamlit_available = True
        except ImportError:
            # 创建一个模拟的streamlit对象，避免执行错误
            class MockStreamlit:
                def __getattr__(self, name):
                    def mock_method(*args, **kwargs):
                        print(f"[模拟] st.{name}() 被调用，但Streamlit不可用")
                        return None
                    return mock_method

            st = MockStreamlit()
            streamlit_available = False

        try:
            import plotly.express as px
            import plotly.graph_objects as go
            plotly_available = True
        except ImportError:
            px = None
            go = None
            plotly_available = False

        exec_globals = {
            'df': df_copy,  # 使用副本而不是原始DataFrame
            'pd': __import__('pandas'),
            'np': __import__('numpy'),
            'st': st,  # Streamlit支持
            'px': px,  # Plotly Express支持
            'go': go,  # Plotly Graph Objects支持
            'save_chart': save_chart_wrapper,
            'print': lambda *args, **kwargs: print(*args, **kwargs, file=output_buffer)
        }

        # 清理代码中的重复导入，避免冲突
        cleaned_code = code

        # 应用深度图表修复
        cleaned_code = apply_deep_chart_fix_standalone(cleaned_code)

        # 应用DataFrame显示修复 - 将print()转换为Streamlit组件
        cleaned_code = convert_print_to_streamlit(cleaned_code)

        # 移除可能导致冲突的导入语句，因为我们已经在exec_globals中提供了这些变量
        import_lines_to_remove = [
            'import streamlit as st',
            'import pandas as pd',
            'import numpy as np',
            'import matplotlib.pyplot as plt'
        ]

        for import_line in import_lines_to_remove:
            if import_line in cleaned_code:
                cleaned_code = cleaned_code.replace(import_line, f'# {import_line} # 已在执行环境中提供')

        # 修复列名引用问题（关键修复）
        print("🔧 修复列名引用问题...")

        # 获取清理前后的列名映射
        original_columns = list(df_copy.columns)

        # 模拟数据清理过程以获取新列名
        import re
        cleaned_columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in original_columns]

        # 创建列名映射
        column_mapping = dict(zip(original_columns, cleaned_columns))

        print(f"列名映射: {column_mapping}")

        # 替换代码中的列名引用
        for old_col, new_col in column_mapping.items():
            if old_col != new_col:
                # 替换各种可能的列名引用格式
                patterns_to_replace = [
                    f"'{old_col}'",
                    f'"{old_col}"',
                    f"['{old_col}']",
                    f'["{old_col}"]'
                ]

                for pattern in patterns_to_replace:
                    replacement = pattern.replace(old_col, new_col)
                    cleaned_code = cleaned_code.replace(pattern, replacement)

        print(f"🔧 清理后的代码:")
        print(cleaned_code)

        # 添加图表持久化机制
        if any(chart_method in cleaned_code for chart_method in ['st.bar_chart', 'st.line_chart', 'st.plotly_chart']):
            print("🔒 启用图表持久化机制")
            # 在代码前添加持久化设置
            persistence_code = """
# 图表持久化设置
import streamlit as st
if 'chart_rendered' not in st.session_state:
    st.session_state.chart_rendered = False

"""
            cleaned_code = persistence_code + cleaned_code

        with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
            exec(cleaned_code, exec_globals)

        # 获取输出
        output = output_buffer.getvalue()
        error_output = error_buffer.getvalue()

        if error_output:
            result['error'] = error_output
            print(f"❌ 执行警告: {error_output}")

        if output:
            result['output'] = output
            print(output)

        # 检查图表类型和显示方式
        uses_streamlit_native = any(chart_type in code for chart_type in [
            'st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart'
        ])
        uses_plotly_native = 'st.plotly_chart' in code

        # 检查是否有Plotly图表但缺少st.plotly_chart调用
        has_plotly_fig = ('fig = px.' in code or 'fig=px.' in code) and 'plotly' in code
        if has_plotly_fig and not uses_plotly_native:
            print("⚠️ 检测到Plotly图表但缺少st.plotly_chart调用，正在修复...")
            # 在代码末尾添加st.plotly_chart调用
            # 检查多种可能的截断情况
            code_lines = code.strip().split('\n')
            last_line = code_lines[-1].strip() if code_lines else ''

            # 如果代码以fig相关操作结尾但没有st.plotly_chart，则添加
            if (last_line.startswith('fig') or 'fig.' in last_line) and 'st.plotly_chart' not in code:
                # 添加st.plotly_chart调用
                code = code.rstrip() + '\nst.plotly_chart(fig, use_container_width=True)'
                uses_plotly_native = True
                print("✅ 已自动修复Plotly图表显示")
                # 重新执行修复后的代码
                try:
                    output_buffer = io.StringIO()
                    error_buffer = io.StringIO()
                    with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
                        exec(code, exec_globals)
                    result['code'] = code  # 更新代码
                    # 更新输出
                    new_output = output_buffer.getvalue()
                    if new_output:
                        result['output'] = result.get('output', '') + '\n' + new_output
                except Exception as e:
                    print(f"❌ 修复后执行失败: {e}")

        if uses_streamlit_native:
            # Streamlit原生图表组件
            result['uses_streamlit_native'] = True
            result['uses_plotly_native'] = False
            result['has_chart'] = False  # 不需要额外显示
            print(f"📊 Streamlit原生图表已显示")
        elif uses_plotly_native:
            # Plotly原生图表，需要在Streamlit上下文中重新执行显示部分
            result['uses_streamlit_native'] = False
            result['uses_plotly_native'] = True
            result['has_chart'] = True  # 需要在前端重新显示
            result['plotly_code'] = cleaned_code  # 保存清理后的代码用于前端执行
            print(f"📊 Plotly原生图表代码已准备，将在前端显示")
        elif chart_figure is not None:
            # Matplotlib图表对象
            result['chart_figure'] = chart_figure
            result['has_chart'] = True
            result['uses_streamlit_native'] = False
            result['uses_plotly_native'] = False
            print(f"📊 图表已生成（matplotlib对象）")
        elif os.path.exists(chart_path):
            # 保存的图表文件
            result['chart_path'] = chart_path
            result['has_chart'] = True
            result['uses_streamlit_native'] = False
            result['uses_plotly_native'] = False
            print(f"📊 图表已生成: {chart_path}")
        else:
            result['has_chart'] = False
            result['uses_streamlit_native'] = False
            result['uses_plotly_native'] = False

        result['success'] = True
        print("✅ 执行成功")

        # 显示元数据使用情况
        if result.get('metadata_used'):
            print("🎯 已使用元数据增强分析准确性")

    except Exception as e:
        result['error'] = str(e)
        result['success'] = False
        print(f"❌ 执行失败: {e}")

    print("-" * 50)
    return result

def demo_comprehensive_analysis():
    """综合演示"""
    print("🎯 通义千问 + PandasAI 综合演示")
    print("=" * 50)
    
    # 创建综合数据集
    data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch', 'Mac Mini'],
        '类别': ['手机', '平板', '笔记本', '配件', '配件', '台式机'],
        '价格': [6999, 4599, 14999, 1899, 3199, 4999],
        '销量': [1200, 800, 400, 1500, 1000, 300],
        '库存': [150, 200, 80, 300, 250, 100],
        '评分': [4.8, 4.6, 4.9, 4.7, 4.5, 4.4],
        '上市月份': ['2023-09', '2023-10', '2023-11', '2023-09', '2023-10', '2023-11']
    }
    
    df = pd.DataFrame(data)
    print("📊 数据集概览:")
    print(df)
    print()
    
    # 执行各种分析
    queries = [
        "计算总销售额（价格×销量）",
        "找出销量最高的产品名称",
        "计算每个类别的平均价格",
        "找出评分最高的产品",
        "计算库存总价值（价格×库存）",
        "显示价格超过5000的产品",
        "按销量降序排列显示前3名产品",
        "计算配件类产品的总销量"
    ]
    
    for query in queries:
        analyze_data(df, query)

def test_chinese_data():
    """测试中文数据处理"""
    print("\n🇨🇳 中文数据处理测试")
    print("=" * 50)
    
    # 中文数据集
    data = {
        '城市': ['北京', '上海', '广州', '深圳', '杭州', '成都'],
        '人口万人': [2154, 2424, 1530, 1756, 1220, 1658],
        'GDP万亿': [4.03, 4.32, 2.82, 3.24, 1.81, 2.08],
        '房价元平米': [65000, 70000, 45000, 55000, 35000, 25000],
        '地区': ['华北', '华东', '华南', '华南', '华东', '西南']
    }
    
    df = pd.DataFrame(data)
    print("📊 中文数据集:")
    print(df)
    print()
    
    chinese_queries = [
        "哪个城市的GDP最高？",
        "人口超过2000万的城市有哪些？",
        "计算华东地区的平均房价",
        "按人口排序显示所有城市",
        "找出房价最便宜的城市",
        "计算所有城市的平均GDP"
    ]
    
    for query in chinese_queries:
        analyze_data(df, query)

def main():
    """主函数"""
    print("🎉 通义千问完美集成演示")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("请在.env文件中配置: DASHSCOPE_API_KEY=your-api-key")
        return
    
    print(f"✅ API密钥已配置: {api_key[:10]}...{api_key[-4:]}")
    print()
    
    try:
        # 综合演示
        demo_comprehensive_analysis()
        
        # 中文数据测试
        test_chinese_data()
        
        print("\n" + "=" * 60)
        print("🎊 演示完成!")
        print("\n✅ 集成特点:")
        print("- 完美支持中文自然语言查询")
        print("- 生成准确可执行的Python代码")
        print("- 支持复杂数据分析任务")
        print("- 强大的代码清理和错误处理")
        print("- 适用于各种数据类型和查询")
        
        print("\n🚀 现在您可以:")
        print("1. 使用中文进行数据查询")
        print("2. 进行复杂的数据分析")
        print("3. 处理各种类型的数据集")
        print("4. 获得准确的分析结果")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
