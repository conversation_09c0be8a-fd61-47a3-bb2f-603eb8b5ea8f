#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的上下文管理器
集成智能引用检测系统
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class ReferenceIntent:
    """引用意图数据类"""
    intent_type: str
    confidence: float
    referenced_elements: List[str]
    action_type: str

class EnhancedContextManager:
    """增强的上下文管理器"""
    
    def __init__(self, enable_logging: bool = True):
        self.enable_logging = enable_logging
        self.logger = None
        if enable_logging:
            import logging
            self.logger = logging.getLogger(__name__)
        
        # 智能检测配置
        self.semantic_patterns = {
            'continuation': {
                'patterns': [
                    r'在.{0,5}基础上|基于|根据|参考|延续|继续|接着',
                    r'结合.{0,10}结果|利用.{0,10}分析|使用.{0,10}数据',
                    r'进一步|深入|详细|具体|然后|接下来',
                ],
                'confidence_base': 0.8
            },
            'modification': {
                'patterns': [
                    r'修改|调整|改变|优化',
                    r'重新|再次|重做',
                    r'换成|改为|变成'
                ],
                'confidence_base': 0.8
            }
        }
        
        self.reference_indicators = {
            'analysis': ['分析', '结果', '数据', '统计', '计算', '销售额', '地区', '产品'],
            'chart': ['图', '图表', '图形', '可视化', '展示'],
            'table': ['表', '表格', '数据表', '列表']
        }
    
    def intelligent_detect_references(self, instruction: str, conversation_history: List[Dict], 
                                    reference_tracker: Dict) -> Dict[str, Any]:
        """
        智能检测引用
        
        Args:
            instruction: 用户指令
            conversation_history: 对话历史
            reference_tracker: 引用跟踪器
            
        Returns:
            包含引用信息和意图的字典
        """
        instruction_lower = instruction.lower()
        
        # 1. 检测引用意图
        intent = self._detect_reference_intent(instruction_lower, conversation_history)
        
        # 2. 构建引用信息
        references = {}
        if intent and intent.confidence >= 0.2:
            references = self._build_references_from_intent(intent, reference_tracker, conversation_history)
        
        # 3. 生成智能指导
        guidance = self._generate_contextual_guidance(intent, conversation_history) if intent else []
        
        result = {
            'references': references,
            'intent': intent,
            'guidance': guidance,
            'has_reference': len(references) > 0 or (intent and intent.confidence >= 0.3)
        }
        
        if self.logger:
            self.logger.info(f"🧠 智能引用检测结果:")
            if intent:
                self.logger.info(f"   意图类型: {intent.intent_type}")
                self.logger.info(f"   置信度: {intent.confidence:.2f}")
                self.logger.info(f"   引用元素: {intent.referenced_elements}")
            self.logger.info(f"   检测到引用: {list(references.keys()) if references else '无'}")
        
        return result
    
    def _detect_reference_intent(self, instruction: str, conversation_history: List[Dict]) -> Optional[ReferenceIntent]:
        """检测引用意图"""
        best_intent = None
        max_confidence = 0.0
        
        # 语义模式匹配
        for intent_type, config in self.semantic_patterns.items():
            confidence = self._calculate_semantic_confidence(instruction, config)
            if confidence > max_confidence:
                max_confidence = confidence
                best_intent = intent_type
        
        # 上下文推理
        if max_confidence < 0.4 and conversation_history:
            context_confidence = self._analyze_contextual_clues(instruction, conversation_history)
            if context_confidence > max_confidence:
                max_confidence = context_confidence
                best_intent = 'continuation'
        
        # 构建意图对象
        if max_confidence >= 0.2:
            referenced_elements = self._identify_referenced_elements(instruction, conversation_history)
            action_type = self._identify_action_type(instruction)
            
            return ReferenceIntent(
                intent_type=best_intent,
                confidence=max_confidence,
                referenced_elements=referenced_elements,
                action_type=action_type
            )
        
        return None
    
    def _calculate_semantic_confidence(self, instruction: str, config: Dict) -> float:
        """计算语义置信度"""
        confidence = 0.0
        pattern_matches = 0

        for pattern in config['patterns']:
            if re.search(pattern, instruction):
                pattern_matches += 1

        if pattern_matches > 0:
            confidence = config['confidence_base'] * (pattern_matches / len(config['patterns']))

        return min(confidence, 1.0)
    
    def _analyze_contextual_clues(self, instruction: str, conversation_history: List[Dict]) -> float:
        """分析上下文线索"""
        if not conversation_history:
            return 0.0
        
        confidence = 0.0
        last_round = conversation_history[-1]
        
        # 检查是否涉及历史分析的关键概念
        if last_round.get('code'):
            key_concepts = self._extract_key_concepts(last_round['code'])
            concept_matches = sum(1 for concept in key_concepts if concept in instruction)
            
            if concept_matches > 0:
                confidence += 0.5 * (concept_matches / max(len(key_concepts), 1))
        
        # 检查隐含的时间/延续表达
        implicit_patterns = [
            r'然后|接下来|下一步|现在|这次',
            r'同样|类似|相同|一样',
            r'另外|其他|别的|不同',
            r'还要|还需要|同时|以及'
        ]
        
        for pattern in implicit_patterns:
            if re.search(pattern, instruction):
                confidence += 0.3
                break
        
        return min(confidence, 1.0)
    
    def _extract_key_concepts(self, code: str) -> List[str]:
        """从代码中提取关键概念"""
        concepts = []
        code_lower = code.lower()
        
        # 提取变量名
        var_matches = re.findall(r'(\w+)\s*=', code_lower)
        concepts.extend(var_matches)
        
        # 提取分组字段
        groupby_matches = re.findall(r'groupby\([\'"]([^\'"]+)[\'"]\)', code_lower)
        concepts.extend(groupby_matches)
        
        # 提取列名
        column_matches = re.findall(r'\[[\'"]([^\'"]+)[\'"]\]', code_lower)
        concepts.extend(column_matches)
        
        return list(set(concepts))
    
    def _identify_referenced_elements(self, instruction: str, conversation_history: List[Dict]) -> List[str]:
        """识别引用的元素类型"""
        referenced = []
        
        for element_type, indicators in self.reference_indicators.items():
            score = 0
            
            # 检查指示词
            for indicator in indicators:
                if indicator in instruction:
                    score += 1
            
            # 检查历史相关性
            if conversation_history and conversation_history[-1].get('code'):
                last_code = conversation_history[-1]['code'].lower()
                relevance_keywords = {
                    'analysis': ['groupby', 'sum', 'mean', 'count'],
                    'chart': ['chart', 'plot', 'bar_chart'],
                    'table': ['dataframe', 'table', 'reset_index']
                }
                
                keywords = relevance_keywords.get(element_type, [])
                history_score = sum(1 for keyword in keywords if keyword in last_code)
                score += history_score * 0.5
            
            if score >= 1.0:
                referenced.append(element_type)
        
        return referenced
    
    def _identify_action_type(self, instruction: str) -> str:
        """识别动作类型"""
        action_patterns = {
            'analyze': ['分析', '研究', '探索', '查看', '统计', '计算'],
            'modify': ['修改', '调整', '改变', '优化'],
            'compare': ['对比', '比较', '对照'],
            'extend': ['增加', '补充', '扩展', '添加']
        }
        
        for action_type, keywords in action_patterns.items():
            if any(keyword in instruction for keyword in keywords):
                return action_type
        
        return 'analyze'
    
    def _build_references_from_intent(self, intent: ReferenceIntent, reference_tracker: Dict, 
                                    conversation_history: List[Dict]) -> Dict[str, Any]:
        """基于意图构建引用信息"""
        references = {}
        
        # 根据引用元素类型添加相应的引用
        for element_type in intent.referenced_elements:
            tracker_key = f'last_{element_type}'
            if reference_tracker.get(tracker_key):
                references[element_type] = reference_tracker[tracker_key]
        
        # 如果没有找到具体的引用，但置信度较高，添加通用的分析引用
        if not references and intent.confidence >= 0.5 and reference_tracker.get('last_analysis'):
            references['analysis'] = reference_tracker['last_analysis']
        
        return references
    
    def _generate_contextual_guidance(self, intent: ReferenceIntent, conversation_history: List[Dict]) -> List[str]:
        """生成上下文指导"""
        guidance = []
        
        if intent.intent_type == 'continuation':
            guidance.append(f"用户希望基于之前的分析结果进行{intent.action_type}")
            guidance.append("请复用相关变量和数据，确保分析的连贯性")
            
            if conversation_history:
                last_code = conversation_history[-1].get('code', '')
                key_vars = self._extract_key_concepts(last_code)
                if key_vars:
                    guidance.append(f"建议复用的关键变量: {', '.join(key_vars[:3])}")
        
        elif intent.intent_type == 'modification':
            guidance.append("用户希望修改之前的分析")
            guidance.append("请生成改进版本的代码，保持核心逻辑的同时进行优化")
        
        # 根据置信度添加额外指导
        if intent.confidence >= 0.7:
            guidance.append("高置信度引用：强烈建议基于历史分析结果进行扩展")
        elif intent.confidence >= 0.5:
            guidance.append("中等置信度引用：建议参考历史分析结果")
        else:
            guidance.append("低置信度引用：可以考虑参考历史分析结果")
        
        return guidance

# 测试增强的上下文管理器
def test_enhanced_context_manager():
    """测试增强的上下文管理器"""
    print("🚀 增强上下文管理器测试")
    print("=" * 60)
    
    manager = EnhancedContextManager(enable_logging=False)
    
    # 模拟对话历史和引用跟踪器
    conversation_history = [
        {
            'user_message': '分析各地区销售额',
            'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
            'execution_result': {'success': True}
        }
    ]
    
    reference_tracker = {
        'last_analysis': {
            'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
            'success': True,
            'timestamp': '2025-08-05T17:26:43'
        }
    }
    
    # 测试您日志中的实际指令
    test_instruction = "在此基础上，分析各销售员的销售额"
    
    print(f"📝 测试指令: {test_instruction}")
    print("-" * 40)
    
    result = manager.intelligent_detect_references(
        test_instruction, 
        conversation_history, 
        reference_tracker
    )
    
    print(f"✅ 检测结果:")
    print(f"   有引用: {result['has_reference']}")
    print(f"   引用类型: {list(result['references'].keys())}")

    if result['intent']:
        print(f"   意图类型: {result['intent'].intent_type}")
        print(f"   置信度: {result['intent'].confidence:.2f}")
        print(f"   引用元素: {result['intent'].referenced_elements}")
        print(f"   动作类型: {result['intent'].action_type}")
    else:
        print(f"   未检测到明确意图")

    if result['guidance']:
        print(f"   智能指导:")
        for i, guide in enumerate(result['guidance'], 1):
            print(f"     {i}. {guide}")
    else:
        print(f"   无智能指导")

if __name__ == "__main__":
    test_enhanced_context_manager()
