# Windows PowerShell 缓存清理脚本
# 清理Python缓存和Streamlit相关文件

Write-Host "🧹 开始清理Python和Streamlit缓存..." -ForegroundColor Green

# 清理Python字节码文件
Write-Host "📁 清理Python字节码文件..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Filter "*.pyc" | Remove-Item -Force -ErrorAction SilentlyContinue
Write-Host "✅ Python字节码文件清理完成" -ForegroundColor Green

# 清理__pycache__目录
Write-Host "📁 清理__pycache__目录..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Directory -Name "__pycache__" | ForEach-Object {
    $path = Join-Path -Path (Get-Location) -ChildPath $_
    Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
}
Write-Host "✅ __pycache__目录清理完成" -ForegroundColor Green

# 清理.pytest_cache目录
Write-Host "📁 清理.pytest_cache目录..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Directory -Name ".pytest_cache" | ForEach-Object {
    $path = Join-Path -Path (Get-Location) -ChildPath $_
    Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
}
Write-Host "✅ .pytest_cache目录清理完成" -ForegroundColor Green

# 清理Streamlit缓存目录
Write-Host "📁 清理Streamlit缓存..." -ForegroundColor Yellow
$streamlitCache = Join-Path -Path $env:USERPROFILE -ChildPath ".streamlit"
if (Test-Path $streamlitCache) {
    Remove-Item -Path $streamlitCache -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Streamlit缓存清理完成" -ForegroundColor Green
} else {
    Write-Host "ℹ️ 未找到Streamlit缓存目录" -ForegroundColor Blue
}

# 清理临时文件
Write-Host "📁 清理临时文件..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Filter "*.tmp" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Path . -Recurse -Filter "*.temp" | Remove-Item -Force -ErrorAction SilentlyContinue
Write-Host "✅ 临时文件清理完成" -ForegroundColor Green

Write-Host "🎉 所有缓存清理完成！" -ForegroundColor Green
Write-Host "💡 建议现在重启Streamlit应用" -ForegroundColor Cyan
