"""
图表修复处理器 - 负责修复图表代码，确保Streamlit兼容性
"""

import re
from typing import Dict, List, Tuple
from ..utils.logger import get_app_logger


class ChartFixer:
    """
    图表修复处理器
    
    负责修复LLM生成的图表代码，确保与Streamlit兼容。
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化图表修复器
        
        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
    
    def fix_charts(self, code: str, instruction: str = "") -> str:
        """
        修复图表代码，确保Streamlit兼容性

        Args:
            code: 原始代码字符串
            instruction: 用户指令（用于智能修复）

        Returns:
            修复后的代码字符串
        """
        if self.logger:
            self.logger.info("开始修复图表代码")

        # 1. 智能图表选择（根据用户意图选择最合适的图表）
        code = self._smart_chart_selection(code, instruction)

        # 2. 强制转换为Streamlit原生图表
        code = self._force_streamlit_native_charts(code)

        # 3. 添加数据清理代码
        code = self._add_data_cleaning(code)

        # 4. 转换print语句为Streamlit组件
        code = self._convert_print_to_streamlit(code)

        # 5. 修复图表数据格式
        code = self._fix_chart_data_format(code)

        # 6. 添加图表保存功能
        code = self._add_chart_saving(code)

        if self.logger:
            self.logger.info("图表代码修复完成")

        return code

    def _smart_chart_selection(self, code: str, instruction: str) -> str:
        """
        智能图表选择：根据用户意图选择最合适的图表类型，移除不相关的图表
        """
        if self.logger:
            self.logger.info("执行智能图表选择")

        # 检测用户意图
        chart_intent = self._detect_user_intent(instruction)

        if chart_intent == 'none':
            return code

        # 根据意图保留最合适的图表，移除其他图表
        if chart_intent == 'trend':
            # 趋势分析：只保留折线图相关代码
            code = self._keep_only_chart_type(code, ['plt.plot'])
        elif chart_intent == 'proportion':
            # 占比分析：只保留饼图相关代码
            code = self._keep_only_chart_type(code, ['plt.pie'])
        elif chart_intent == 'comparison':
            # 对比分析：只保留柱状图相关代码
            code = self._keep_only_chart_type(code, ['plt.bar'])
        elif chart_intent == 'correlation':
            # 相关性分析：只保留散点图相关代码
            code = self._keep_only_chart_type(code, ['plt.scatter'])

        return code

    def _detect_user_intent(self, instruction: str) -> str:
        """检测用户意图"""
        instruction_lower = instruction.lower()

        # 趋势分析关键词
        trend_keywords = ['趋势', '变化', '时间', '日期', '月份', '年份', '每天', '每月', '每年', 'trend']
        if any(keyword in instruction_lower for keyword in trend_keywords):
            return 'trend'

        # 占比分析关键词
        proportion_keywords = ['占比', '比例', '份额', '百分比', '分布', 'proportion', 'percentage', 'share']
        if any(keyword in instruction_lower for keyword in proportion_keywords):
            return 'proportion'

        # 对比分析关键词
        comparison_keywords = ['对比', '比较', '排名', '排序', 'compare', 'comparison', 'ranking']
        if any(keyword in instruction_lower for keyword in comparison_keywords):
            return 'comparison'

        # 相关性分析关键词
        correlation_keywords = ['相关', '关系', '关联', 'correlation', 'relationship']
        if any(keyword in instruction_lower for keyword in correlation_keywords):
            return 'correlation'

        return 'none'

    def _keep_only_chart_type(self, code: str, keep_patterns: list) -> str:
        """只保留指定类型的图表代码，移除其他图表"""
        lines = code.split('\n')
        filtered_lines = []
        skip_until_next_section = False

        for line in lines:
            line_stripped = line.strip()

            # 检查是否是matplotlib图表调用
            is_chart_line = any(pattern in line for pattern in ['plt.bar', 'plt.plot', 'plt.scatter', 'plt.pie', 'plt.hist'])

            if is_chart_line:
                # 检查是否是要保留的图表类型
                should_keep = any(pattern in line for pattern in keep_patterns)
                if should_keep:
                    filtered_lines.append(line)
                    skip_until_next_section = False
                else:
                    # 跳过不需要的图表及其相关代码
                    skip_until_next_section = True
                    continue
            elif skip_until_next_section:
                # 跳过图表相关的标题、显示等代码
                if any(skip_keyword in line_stripped for skip_keyword in ['plt.title', 'plt.show', 'plt.xlabel', 'plt.ylabel']):
                    continue
                elif line_stripped.startswith('#') and any(chart_word in line_stripped for chart_word in ['图', 'chart']):
                    continue
                else:
                    # 遇到非图表相关代码，停止跳过
                    skip_until_next_section = False
                    filtered_lines.append(line)
            else:
                filtered_lines.append(line)

        return '\n'.join(filtered_lines)

    def _force_streamlit_native_charts(self, code: str) -> str:
        """强制转换为Streamlit原生图表"""
        if self.logger:
            self.logger.info("转换为Streamlit原生图表")
        
        # matplotlib转换为Streamlit（严格按照Streamlit API规范）
        matplotlib_patterns = [
            (r'plt\.bar\([^)]+\)', self._convert_to_st_bar_chart),
            (r'plt\.plot\([^)]+\)', self._convert_to_st_line_chart),
            (r'plt\.scatter\([^)]+\)', self._convert_to_st_scatter_chart),
            (r'plt\.hist\([^)]+\)', self._convert_to_st_bar_chart),
            (r'plt\.pie\([^)]+\)', self._convert_to_plotly_pie_chart),
        ]

        # plotly转换规则（保持正确的plotly图表，转换错误的）
        plotly_patterns = [
            (r'px\.bar\([^)]+\)', self._convert_px_bar_to_st),
            (r'px\.line\([^)]+\)', self._convert_px_line_to_st),
            (r'px\.scatter\([^)]+\)', self._convert_px_scatter_to_st),
            # px.pie保持不变，因为Streamlit原生不支持饼图
        ]
        
        # 应用matplotlib转换（避免重复转换）
        converted_patterns = set()
        for pattern, converter in matplotlib_patterns:
            matches = list(re.finditer(pattern, code))
            for match in matches:
                original = match.group(0)
                if original not in converted_patterns:
                    converted = converter(original)
                    code = code.replace(original, converted, 1)  # 只替换第一个匹配
                    converted_patterns.add(original)

        # 应用plotly转换（除了饼图，避免重复转换）
        for pattern, converter in plotly_patterns:
            matches = list(re.finditer(pattern, code))
            for match in matches:
                original = match.group(0)
                if original not in converted_patterns:
                    converted = converter(original)
                    code = code.replace(original, converted, 1)  # 只替换第一个匹配
                    converted_patterns.add(original)
        
        # 移除matplotlib相关的显示代码
        code = re.sub(r'plt\.show\(\)', '', code)
        code = re.sub(r'plt\.tight_layout\(\)', '', code)
        
        # 确保导入streamlit
        if 'st.' in code and 'import streamlit as st' not in code:
            code = 'import streamlit as st\n' + code
        
        return code
    
    def _convert_to_st_bar_chart(self, matplotlib_code: str) -> str:
        """转换matplotlib柱状图为Streamlit柱状图（严格按照Streamlit API规范）"""
        return '''
# 创建柱状图 - 严格按照Streamlit API规范
categorical_cols = df.select_dtypes(include=['object', 'category']).columns
numeric_cols = df.select_dtypes(include=[np.number]).columns

if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
    cat_col, num_col = categorical_cols[0], numeric_cols[0]
    # 数据聚合和排序
    chart_data = df.groupby(cat_col)[num_col].sum().sort_values(ascending=False)
    st.subheader(f"📊 {cat_col} 的 {num_col} 柱状图")
    st.bar_chart(chart_data, use_container_width=True)
else:
    # 备用方案：直接显示数值列
    if len(numeric_cols) >= 1:
        st.subheader("📊 数据柱状图")
        st.bar_chart(df[numeric_cols[0]], use_container_width=True)
    else:
        st.warning("数据中没有合适的列来创建柱状图")
'''
    
    def _convert_to_st_line_chart(self, matplotlib_code: str) -> str:
        """转换matplotlib线图为Streamlit线图（严格按照Streamlit API规范）"""
        return '''
# 创建折线图 - 严格按照Streamlit API规范
datetime_cols = df.select_dtypes(include=['datetime64']).columns
numeric_cols = df.select_dtypes(include=[np.number]).columns

if len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
    # 时间序列数据
    date_col, num_col = datetime_cols[0], numeric_cols[0]
    chart_data = df.groupby(date_col)[num_col].sum()
    st.subheader(f"📈 {num_col} 时间趋势")
    st.line_chart(chart_data, use_container_width=True)
elif len(numeric_cols) >= 1:
    # 备用方案：使用索引作为x轴
    st.subheader("📈 数据趋势")
    st.line_chart(df[numeric_cols[0]], use_container_width=True)
else:
    st.warning("数据中没有合适的数值列来创建折线图")
'''
    
    def _convert_to_st_scatter_chart(self, matplotlib_code: str) -> str:
        """转换matplotlib散点图为Streamlit散点图（严格按照Streamlit API规范）"""
        return '''
# 创建散点图 - 严格按照Streamlit API规范
numeric_cols = df.select_dtypes(include=[np.number]).columns
if len(numeric_cols) >= 2:
    # 使用前两个数值列作为x和y
    x_col, y_col = numeric_cols[0], numeric_cols[1]
    st.subheader(f"📊 {x_col} 与 {y_col} 关系散点图")
    st.scatter_chart(df, x=x_col, y=y_col, use_container_width=True)
else:
    st.warning("数据中没有足够的数值列来创建散点图")
'''

    def _convert_to_plotly_pie_chart(self, matplotlib_code: str) -> str:
        """转换matplotlib饼图为Plotly饼图（Streamlit原生不支持饼图）"""
        return '''
# 创建饼图 - 使用Plotly（Streamlit原生不支持饼图）
import plotly.express as px

# 自动检测分类列和数值列
categorical_cols = df.select_dtypes(include=['object', 'category']).columns
numeric_cols = df.select_dtypes(include=[np.number]).columns

if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
    cat_col, num_col = categorical_cols[0], numeric_cols[0]
    # 数据聚合
    pie_data = df.groupby(cat_col)[num_col].sum().reset_index()

    # 创建饼图
    fig = px.pie(pie_data, values=num_col, names=cat_col,
                 title=f"{cat_col} 占比分布")
    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(showlegend=True)

    st.subheader("🥧 占比分析饼图")
    st.plotly_chart(fig, use_container_width=True)
else:
    st.warning("数据中没有合适的分类列和数值列来创建饼图")
'''

    def _convert_px_bar_to_st(self, plotly_code: str) -> str:
        """转换plotly柱状图为Streamlit原生柱状图"""
        return '''
# 转换为Streamlit原生柱状图
categorical_cols = df.select_dtypes(include=['object', 'category']).columns
numeric_cols = df.select_dtypes(include=[np.number]).columns

if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
    cat_col, num_col = categorical_cols[0], numeric_cols[0]
    chart_data = df.groupby(cat_col)[num_col].sum()
    st.subheader("📊 柱状图")
    st.bar_chart(chart_data, use_container_width=True)
'''

    def _convert_px_line_to_st(self, plotly_code: str) -> str:
        """转换plotly折线图为Streamlit原生折线图"""
        return '''
# 转换为Streamlit原生折线图
datetime_cols = df.select_dtypes(include=['datetime64']).columns
numeric_cols = df.select_dtypes(include=[np.number]).columns

if len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
    date_col, num_col = datetime_cols[0], numeric_cols[0]
    chart_data = df.groupby(date_col)[num_col].sum()
    st.subheader("📈 折线图")
    st.line_chart(chart_data, use_container_width=True)
else:
    # 备用方案：使用索引作为x轴
    if len(numeric_cols) >= 1:
        st.subheader("📈 折线图")
        st.line_chart(df[numeric_cols[0]], use_container_width=True)
'''

    def _convert_px_scatter_to_st(self, plotly_code: str) -> str:
        """转换plotly散点图为Streamlit原生散点图"""
        return '''
# 转换为Streamlit原生散点图
numeric_cols = df.select_dtypes(include=[np.number]).columns
if len(numeric_cols) >= 2:
    x_col, y_col = numeric_cols[0], numeric_cols[1]
    st.subheader(f"📊 {x_col} 与 {y_col} 散点图")
    st.scatter_chart(df, x=x_col, y=y_col, use_container_width=True)
'''
    
    def _add_data_cleaning(self, code: str) -> str:
        """添加简化的数据清理代码"""
        if any(chart_method in code for chart_method in ['st.bar_chart', 'st.line_chart', 'st.area_chart', 'st.scatter_chart', 'st.plotly_chart']):
            # 简化的数据清理代码，避免复杂操作导致错误
            # 检查是否需要plotly
            needs_plotly = 'plotly' in code.lower() or 'px.' in code

            data_cleaning_code = '''# 基础数据清理和导入
import numpy as np
import pandas as pd
import streamlit as st
'''

            # 如果需要plotly，添加导入
            if needs_plotly:
                data_cleaning_code += '''
# 导入plotly用于交互式图表
import plotly.express as px
'''

            data_cleaning_code += '''
# 确保数据类型正确
if 'df' in locals() or 'df' in globals():
    # 处理数值列中的NaN值
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        df[numeric_cols] = df[numeric_cols].fillna(0)

    # 处理无穷大值
    df = df.replace([np.inf, -np.inf], np.nan).fillna(0)

'''
            # 在代码开头插入清理代码
            code = data_cleaning_code + '\n' + code

        return code
    
    def _convert_print_to_streamlit(self, code: str) -> str:
        """将print()语句转换为Streamlit组件显示"""
        if self.logger:
            self.logger.info("转换print()为Streamlit组件")
        
        lines = code.split('\n')
        converted_lines = []
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检查是否是print语句
            if stripped_line.startswith('print(') and stripped_line.endswith(')'):
                # 提取print的内容
                print_content = stripped_line[6:-1]  # 移除 'print(' 和 ')'
                
                # 判断print的内容类型并转换
                if print_content.startswith('"') or print_content.startswith("'"):
                    # 字符串内容，使用st.write
                    converted_line = f"st.write({print_content})"
                elif 'grouped_data' in print_content or 'data' in print_content:
                    # DataFrame或Series数据，使用st.dataframe
                    if print_content in ['grouped_data', 'data', 'df']:
                        converted_line = f"st.dataframe({print_content}, use_container_width=True)"
                    else:
                        converted_line = f"st.write({print_content})"
                elif 'f"' in print_content or "f'" in print_content:
                    # f-string，使用st.success或st.info
                    if any(keyword in print_content for keyword in ['答案', '最高', '最低', '结果']):
                        converted_line = f"st.success({print_content})"
                    else:
                        converted_line = f"st.info({print_content})"
                else:
                    # 其他内容，使用st.write
                    converted_line = f"st.write({print_content})"
                
                # 保持原有的缩进
                indent = line[:len(line) - len(line.lstrip())]
                converted_lines.append(indent + converted_line)
                
                if self.logger:
                    self.logger.debug(f"转换: {stripped_line} → {converted_line}")
            else:
                # 不是print语句，保持原样
                converted_lines.append(line)
        
        return '\n'.join(converted_lines)
    
    def _fix_chart_data_format(self, code: str) -> str:
        """修复图表数据格式 - 简化版本，避免过度处理"""
        # 暂时禁用自动添加fillna，避免字符串错误
        # 如果需要处理NaN值，让LLM在生成代码时自己处理
        if self.logger:
            self.logger.info("跳过图表数据格式自动修复，避免类型错误")

        return code
    
    def _add_chart_saving(self, code: str) -> str:
        """添加图表保存功能（安全版本，不使用os模块）"""
        # 暂时禁用图表保存功能，避免安全问题
        # 如果需要保存功能，可以在应用层面实现
        if self.logger:
            self.logger.info("跳过图表保存功能，避免安全风险")

        return code
    
    def detect_chart_type(self, instruction: str) -> str:
        """根据指令检测图表类型"""
        instruction_lower = instruction.lower()
        
        if any(keyword in instruction_lower for keyword in ['柱状图', '条形图', 'bar', '柱图']):
            return 'bar'
        elif any(keyword in instruction_lower for keyword in ['线图', '折线图', 'line', '趋势']):
            return 'line'
        elif any(keyword in instruction_lower for keyword in ['散点图', 'scatter', '分布']):
            return 'scatter'
        elif any(keyword in instruction_lower for keyword in ['饼图', 'pie', '占比']):
            return 'pie'
        elif any(keyword in instruction_lower for keyword in ['面积图', 'area', '堆积']):
            return 'area'
        else:
            return 'auto'  # 自动选择
