#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Streamlit启动脚本
自动处理虚拟环境和依赖问题
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🤖 智能数据分析助手 - 简化启动脚本")
    print("=" * 50)
    
    # 检查虚拟环境
    venv_path = Path("venv")
    if not venv_path.exists():
        print("❌ 未找到虚拟环境")
        print("请先创建虚拟环境: python -m venv venv")
        input("按回车键退出...")
        return
    
    # 设置虚拟环境Python路径
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        streamlit_exe = venv_path / "Scripts" / "streamlit.exe"
    else:  # Linux/Mac
        python_exe = venv_path / "bin" / "python"
        streamlit_exe = venv_path / "bin" / "streamlit"
    
    if not python_exe.exists():
        print("❌ 虚拟环境Python不存在")
        input("按回车键退出...")
        return
    
    print("✅ 虚拟环境检查通过")
    
    # 检查基本依赖
    print("📋 检查基本依赖...")
    try:
        result = subprocess.run([
            str(python_exe), "-c", 
            "import streamlit; print('Streamlit OK')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("⚠️ Streamlit未安装，正在安装...")
            subprocess.run([
                str(python_exe), "-m", "pip", "install", "streamlit"
            ], check=True)
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        input("按回车键退出...")
        return
    
    print("✅ 基本依赖检查完成")
    
    # 检查环境变量
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ 未找到.env文件，创建示例文件...")
        with open(".env.example", "w", encoding="utf-8") as f:
            f.write("# 通义千问API密钥\n")
            f.write("DASHSCOPE_API_KEY=your-dashscope-api-key-here\n")
        print("❌ 请先配置.env文件中的API密钥")
        print("参考.env.example文件")
        input("按回车键退出...")
        return
    
    print("✅ 环境配置检查完成")
    
    # 创建必要目录
    directories = ["uploaded_files", "chat_history", "charts", "temp"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")
    
    # 启动Streamlit
    print("\n🚀 启动Streamlit应用...")
    print("🌐 应用将在浏览器中打开: http://localhost:8501")
    print("按 Ctrl+C 停止应用")
    print("-" * 50)
    
    try:
        if streamlit_exe.exists():
            subprocess.run([
                str(streamlit_exe), "run", "streamlit_app.py",
                "--server.port", "8501",
                "--server.address", "localhost"
            ])
        else:
            subprocess.run([
                str(python_exe), "-m", "streamlit", "run", "streamlit_app.py",
                "--server.port", "8501",
                "--server.address", "localhost"
            ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
