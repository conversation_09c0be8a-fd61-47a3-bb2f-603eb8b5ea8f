"""
统一应用配置管理

整合所有配置项，提供统一的配置接口。
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv

# 加载环境变量 - 从项目根目录加载
load_dotenv(Path(__file__).parent.parent / 'config' / '.env')

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent


@dataclass
class DatabaseConfig:
    """数据库配置"""
    # 如果将来需要数据库支持
    url: str = "sqlite:///data/app.db"
    echo: bool = False
    pool_size: int = 5


@dataclass
class LLMConfig:
    """LLM配置"""
    # 通义千问配置
    tongyi_api_key: str = field(default_factory=lambda: os.getenv('TONGYI_API_KEY', ''))
    tongyi_model: str = field(default_factory=lambda: os.getenv('TONGYI_MODEL', 'qwen-plus'))
    tongyi_temperature: float = field(default_factory=lambda: float(os.getenv('TONGYI_TEMPERATURE', '0.1')))
    tongyi_max_tokens: int = field(default_factory=lambda: int(os.getenv('TONGYI_MAX_TOKENS', '2000')))
    tongyi_timeout: int = field(default_factory=lambda: int(os.getenv('TONGYI_TIMEOUT', '30')))
    
    # 功能开关
    enable_chart_fix: bool = field(default_factory=lambda: os.getenv('ENABLE_CHART_FIX', 'true').lower() == 'true')
    enable_metadata: bool = field(default_factory=lambda: os.getenv('ENABLE_METADATA', 'true').lower() == 'true')  # 默认启用元数据
    enable_logging: bool = field(default_factory=lambda: os.getenv('ENABLE_LOGGING', 'true').lower() == 'true')
    
    # 重试配置
    retry_attempts: int = field(default_factory=lambda: int(os.getenv('RETRY_ATTEMPTS', '3')))
    retry_delay: float = field(default_factory=lambda: float(os.getenv('RETRY_DELAY', '1.0')))


@dataclass
class StreamlitConfig:
    """Streamlit配置"""
    page_title: str = "AI数据分析平台"
    page_icon: str = "📊"
    layout: str = "wide"
    initial_sidebar_state: str = "collapsed"
    
    # 主题配置
    theme_primary_color: str = "#1f77b4"
    theme_background_color: str = "#ffffff"
    theme_secondary_background_color: str = "#f0f2f6"
    theme_text_color: str = "#262730"
    
    # 服务器配置
    server_port: int = field(default_factory=lambda: int(os.getenv('STREAMLIT_PORT', '8501')))
    server_address: str = field(default_factory=lambda: os.getenv('STREAMLIT_ADDRESS', 'localhost'))
    server_headless: bool = field(default_factory=lambda: os.getenv('STREAMLIT_HEADLESS', 'false').lower() == 'true')


@dataclass
class DataConfig:
    """数据配置"""
    # 目录配置
    uploaded_files_dir: Path = PROJECT_ROOT / "uploaded_files"
    chat_history_dir: Path = PROJECT_ROOT / "chat_history"
    charts_dir: Path = PROJECT_ROOT / "charts"
    cache_dir: Path = PROJECT_ROOT / "cache"
    logs_dir: Path = PROJECT_ROOT / "logs"
    metadata_config_dir: Path = PROJECT_ROOT / "metadata_config"
    
    # 文件配置
    max_file_size_mb: int = field(default_factory=lambda: int(os.getenv('MAX_FILE_SIZE_MB', '200')))
    supported_file_types: tuple = ('.csv', '.xlsx', '.xls', '.json', '.txt')
    
    # 数据处理配置
    auto_clean_data: bool = field(default_factory=lambda: os.getenv('AUTO_CLEAN_DATA', 'true').lower() == 'true')
    max_rows_preview: int = field(default_factory=lambda: int(os.getenv('MAX_ROWS_PREVIEW', '1000')))
    
    def __post_init__(self):
        """创建必要的目录"""
        for dir_path in [
            self.uploaded_files_dir,
            self.chat_history_dir,
            self.charts_dir,
            self.cache_dir,
            self.logs_dir,
            self.metadata_config_dir
        ]:
            dir_path.mkdir(parents=True, exist_ok=True)


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = field(default_factory=lambda: os.getenv('LOG_LEVEL', 'INFO'))
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format: str = '%Y-%m-%d %H:%M:%S'
    
    # 文件日志配置
    enable_file_logging: bool = field(default_factory=lambda: os.getenv('ENABLE_FILE_LOGGING', 'true').lower() == 'true')
    log_file_max_size: int = field(default_factory=lambda: int(os.getenv('LOG_FILE_MAX_SIZE', '10485760')))  # 10MB
    log_file_backup_count: int = field(default_factory=lambda: int(os.getenv('LOG_FILE_BACKUP_COUNT', '5')))
    
    # 控制台日志配置
    enable_console_logging: bool = field(default_factory=lambda: os.getenv('ENABLE_CONSOLE_LOGGING', 'true').lower() == 'true')


@dataclass
class SecurityConfig:
    """安全配置"""
    # API密钥验证
    require_api_key: bool = field(default_factory=lambda: os.getenv('REQUIRE_API_KEY', 'true').lower() == 'true')
    
    # 代码执行安全
    allow_file_operations: bool = field(default_factory=lambda: os.getenv('ALLOW_FILE_OPERATIONS', 'false').lower() == 'true')
    allow_network_operations: bool = field(default_factory=lambda: os.getenv('ALLOW_NETWORK_OPERATIONS', 'false').lower() == 'true')
    
    # 会话配置
    session_timeout_minutes: int = field(default_factory=lambda: int(os.getenv('SESSION_TIMEOUT_MINUTES', '60')))


@dataclass
class PerformanceConfig:
    """性能配置"""
    # 缓存配置
    enable_caching: bool = field(default_factory=lambda: os.getenv('ENABLE_CACHING', 'true').lower() == 'true')
    cache_ttl_seconds: int = field(default_factory=lambda: int(os.getenv('CACHE_TTL_SECONDS', '3600')))
    
    # 并发配置
    max_concurrent_requests: int = field(default_factory=lambda: int(os.getenv('MAX_CONCURRENT_REQUESTS', '10')))
    request_timeout_seconds: int = field(default_factory=lambda: int(os.getenv('REQUEST_TIMEOUT_SECONDS', '30')))


@dataclass
class AppConfig:
    """应用主配置类"""
    # 应用信息
    name: str = "AI数据分析平台"
    version: str = "2.0.0"
    description: str = "基于通义千问的智能数据分析平台"
    
    # 环境配置
    environment: str = field(default_factory=lambda: os.getenv('ENVIRONMENT', 'development'))
    debug: bool = field(default_factory=lambda: os.getenv('DEBUG', 'false').lower() == 'true')
    
    # 子配置
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    streamlit: StreamlitConfig = field(default_factory=StreamlitConfig)
    data: DataConfig = field(default_factory=DataConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'environment': self.environment,
            'debug': self.debug,
            'database': self.database.__dict__,
            'llm': self.llm.__dict__,
            'streamlit': self.streamlit.__dict__,
            'data': {k: str(v) if isinstance(v, Path) else v for k, v in self.data.__dict__.items()},
            'logging': self.logging.__dict__,
            'security': self.security.__dict__,
            'performance': self.performance.__dict__,
        }
    
    def validate(self) -> tuple[bool, list[str]]:
        """验证配置"""
        errors = []
        
        # 验证LLM配置
        if self.security.require_api_key and not self.llm.tongyi_api_key:
            errors.append("TONGYI_API_KEY is required but not set")
        
        if self.llm.tongyi_temperature < 0 or self.llm.tongyi_temperature > 1:
            errors.append("TONGYI_TEMPERATURE must be between 0 and 1")
        
        if self.llm.tongyi_max_tokens < 1:
            errors.append("TONGYI_MAX_TOKENS must be greater than 0")
        
        # 验证数据配置
        if self.data.max_file_size_mb < 1:
            errors.append("MAX_FILE_SIZE_MB must be greater than 0")
        
        # 验证性能配置
        if self.performance.max_concurrent_requests < 1:
            errors.append("MAX_CONCURRENT_REQUESTS must be greater than 0")
        
        return len(errors) == 0, errors
    
    def get_streamlit_config(self) -> Dict[str, Any]:
        """获取Streamlit配置字典"""
        return {
            'page_title': self.streamlit.page_title,
            'page_icon': self.streamlit.page_icon,
            'layout': self.streamlit.layout,
            'initial_sidebar_state': self.streamlit.initial_sidebar_state,
        }


# 全局配置实例
app_config = AppConfig()

# 验证配置
is_valid, errors = app_config.validate()
if not is_valid:
    print("⚠️ 配置验证失败:")
    for error in errors:
        print(f"  - {error}")
    print("请检查环境变量配置")


def get_config() -> AppConfig:
    """获取应用配置实例"""
    return app_config


def reload_config():
    """重新加载配置"""
    global app_config
    load_dotenv(Path(__file__).parent.parent / 'config' / '.env', override=True)
    app_config = AppConfig()
    return app_config
