#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目重组脚本 - 将现有文件重新组织到新的目录结构中
"""

import os
import shutil
from pathlib import Path
from datetime import datetime


def create_directory_structure():
    """创建新的目录结构"""
    directories = [
        "app",
        "app/pages",
        "legacy",
        "legacy/old_integrations",
        "legacy/old_apps",
        "docs",
        "tests",
        "tests/unit",
        "tests/integration",
        "scripts",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def move_files():
    """移动文件到新的目录结构"""
    
    # 文件移动映射
    file_moves = [
        # 主应用文件
        ("streamlit_app_v2.py", "app/main.py"),
        ("streamlit_app.py", "legacy/old_apps/streamlit_app_original.py"),
        ("streamlit_app_basic.py", "legacy/old_apps/streamlit_app_basic.py"),
        
        # 旧的集成文件移到legacy
        ("perfect_tongyi_integration.py", "legacy/old_integrations/perfect_tongyi_integration.py"),
        ("enhanced_tongyi_integration.py", "legacy/old_integrations/enhanced_tongyi_integration.py"),
        ("working_tongyi_integration.py", "legacy/old_integrations/working_tongyi_integration.py"),
        
        # 支持文件
        ("result_formatter.py", "legacy/old_integrations/result_formatter.py"),
        ("data_source_cleaner.py", "legacy/old_integrations/data_source_cleaner.py"),
        
        # 元数据相关文件
        ("metadata_manager.py", "legacy/old_integrations/metadata_manager.py"),
        ("metadata_ui.py", "legacy/old_integrations/metadata_ui.py"),
        ("metadata_inference.py", "legacy/old_integrations/metadata_inference.py"),
        
        # 配置文件
        ("config.py", "config/legacy_config.py"),
        (".env", "config/.env"),
        ("requirements_streamlit.txt", "config/requirements.txt"),
        
        # 文档文件
        ("INTEGRATION_ARCHITECTURE_PLAN.md", "docs/architecture_plan.md"),
        ("MIGRATION_GUIDE.md", "docs/migration_guide.md"),
        ("COMPREHENSIVE_CLEANUP_SUMMARY_20250804.md", "docs/cleanup_summary.md"),
        ("PROJECT_CLEANUP_REPORT_20250804.md", "docs/cleanup_report.md"),
        
        # 脚本文件
        ("start_app.bat", "scripts/start_app.bat"),
        ("reorganize_project.py", "scripts/reorganize_project.py"),
    ]
    
    for source, destination in file_moves:
        if os.path.exists(source):
            # 确保目标目录存在
            dest_dir = Path(destination).parent
            dest_dir.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            shutil.move(source, destination)
            print(f"📁 移动: {source} → {destination}")
        else:
            print(f"⚠️  文件不存在: {source}")


def create_new_app_structure():
    """创建新的应用结构文件"""
    
    # 创建新的主入口文件
    main_app_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI数据分析平台 - 主入口文件

基于模块化架构的新版本应用。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入主应用
from app.main import main

if __name__ == "__main__":
    main()
'''
    
    with open("streamlit_app.py", "w", encoding="utf-8") as f:
        f.write(main_app_content)
    print("✅ 创建新的主入口文件: streamlit_app.py")
    
    # 创建应用配置文件
    app_config_content = '''"""
应用配置文件
"""

from pathlib import Path

# 应用基本信息
APP_NAME = "AI数据分析平台"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "基于通义千问的智能数据分析平台"

# 目录配置
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
CACHE_DIR = PROJECT_ROOT / "cache"

# Streamlit配置
STREAMLIT_CONFIG = {
    "page_title": APP_NAME,
    "page_icon": "📊",
    "layout": "wide",
    "initial_sidebar_state": "collapsed"
}

# 创建必要目录
for directory in [DATA_DIR, LOGS_DIR, CACHE_DIR]:
    directory.mkdir(exist_ok=True)
'''
    
    with open("app/config.py", "w", encoding="utf-8") as f:
        f.write(app_config_content)
    print("✅ 创建应用配置文件: app/config.py")


def create_readme():
    """创建新的README文件"""
    readme_content = '''# 🤖 AI数据分析平台 V2.0

基于通义千问的智能数据分析平台，采用模块化架构设计。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r config/requirements.txt
```

### 2. 配置环境变量
复制 `config/.env.example` 到 `config/.env` 并填入您的API密钥：
```bash
TONGYI_API_KEY=your_api_key_here
```

### 3. 启动应用
```bash
streamlit run streamlit_app.py
```

或使用启动脚本：
```bash
# Windows
scripts/start_app.bat

# Linux/Mac
bash scripts/start_app.sh
```

## 📁 项目结构

```
├── app/                    # 主应用目录
│   ├── main.py            # 主应用文件
│   ├── config.py          # 应用配置
│   └── pages/             # 多页面应用
├── core/                  # 核心模块
│   ├── llm/              # LLM集成
│   ├── processors/       # 数据处理器
│   ├── utils/            # 工具模块
│   └── integrations/     # 外部集成
├── config/               # 配置文件
├── docs/                 # 文档
├── tests/                # 测试文件
├── scripts/              # 脚本文件
├── legacy/               # 旧版本文件
├── data/                 # 数据目录
├── logs/                 # 日志目录
└── cache/                # 缓存目录
```

## 🔧 功能特性

- ✅ 模块化架构设计
- ✅ 通义千问LLM集成
- ✅ 智能代码生成和修复
- ✅ 图表自动修复
- ✅ 元数据支持
- ✅ 多格式数据支持
- ✅ 聊天历史记录
- ✅ 完善的错误处理

## 📖 文档

- [架构设计](docs/architecture_plan.md)
- [迁移指南](docs/migration_guide.md)
- [清理报告](docs/cleanup_summary.md)

## 🔄 从旧版本迁移

如果您正在从旧版本升级，请参考 [迁移指南](docs/migration_guide.md)。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
'''
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ 创建README文件")


def create_gitignore():
    """创建.gitignore文件"""
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Streamlit
.streamlit/

# Data files
data/
uploaded_files/
*.csv
*.xlsx
*.json

# Logs
logs/
*.log

# Cache
cache/
.cache/

# Environment variables
.env
config/.env

# Charts
charts/
*.png
*.jpg
*.jpeg

# Backup
backup/

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
'''
    
    with open(".gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content)
    print("✅ 创建.gitignore文件")


def main():
    """主函数"""
    print("🚀 开始项目重组...")
    print("=" * 50)
    
    # 创建备份
    backup_dir = f"backup/reorganization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    Path(backup_dir).mkdir(parents=True, exist_ok=True)
    print(f"📦 创建重组备份目录: {backup_dir}")
    
    try:
        # 1. 创建目录结构
        print("\n📁 创建目录结构...")
        create_directory_structure()
        
        # 2. 移动文件
        print("\n📁 重组文件...")
        move_files()
        
        # 3. 创建新的应用结构
        print("\n🏗️  创建新应用结构...")
        create_new_app_structure()
        
        # 4. 创建文档
        print("\n📖 创建项目文档...")
        create_readme()
        create_gitignore()
        
        print("\n" + "=" * 50)
        print("✅ 项目重组完成！")
        print("\n📋 下一步操作：")
        print("1. 检查新的目录结构")
        print("2. 测试新的应用入口：streamlit run streamlit_app.py")
        print("3. 验证所有功能正常工作")
        print("4. 如有问题，可从备份目录恢复")
        
    except Exception as e:
        print(f"\n❌ 重组过程中出现错误: {str(e)}")
        print(f"💡 可以从备份目录 {backup_dir} 恢复文件")


if __name__ == "__main__":
    main()
