@echo off
REM AI数据分析平台 V2.0 启动脚本

echo ========================================
echo    AI数据分析平台 V2.0 启动脚本
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 💡 请确保已安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 检查虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境，正在激活...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未发现虚拟环境，使用系统Python
)

REM 检查依赖
echo 🔍 检查依赖包...
python -c "import streamlit, pandas, requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要的依赖包
    echo 💡 正在安装依赖...
    pip install -r config\requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查通过

REM 验证配置
echo 🔧 验证应用配置...
python scripts\validate_config.py
if errorlevel 1 (
    echo ❌ 配置验证失败
    echo 💡 请检查 config\.env 文件中的配置
    pause
    exit /b 1
)

echo ✅ 配置验证通过

REM 启动应用
echo 🚀 启动AI数据分析平台...
echo.
echo 📝 启动信息:
echo    - 应用版本: V2.0
echo    - 访问地址: http://localhost:8501
echo    - 配置文件: config\.env
echo    - 日志目录: logs\
echo.
echo 🔗 浏览器将自动打开应用页面
echo 💡 按 Ctrl+C 停止应用
echo.

REM 设置Streamlit配置
set STREAMLIT_CONFIG_FILE=config\streamlit_config.toml

REM 启动Streamlit应用
streamlit run streamlit_app.py --server.port 8501 --server.address localhost

echo.
echo 👋 应用已停止
pause
