#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结构化提示词和智能指导原则
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm.llm_factory import EnhancedTongyiLLM
from core.llm.tongyi_client import TongyiQianwenClient
from core.utils.config import TongyiConfig

def test_structured_prompt_building():
    """测试结构化提示词构建"""
    print("🧪 测试结构化提示词构建")
    print("=" * 60)
    
    # 创建配置和LLM实例
    config = TongyiConfig(
        api_key="test_key",
        model="qwen-plus",
        temperature=0.1,
        max_tokens=2000,
        enable_logging=False
    )
    client = TongyiQianwenClient(config)
    llm = EnhancedTongyiLLM(client=client)
    
    # 模拟对话上下文
    conversation_context = {
        'recent_rounds': [
            {
                'user_message': '分析2024年各地区销售额',
                'code': '''region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("📊 2024年各地区销售额分析")
st.dataframe(region_sales)
st.bar_chart(region_sales, x='地区', y='销售额')''',
                'execution_result': {'success': True}
            }
        ]
    }
    
    # 测试延续性分析
    print("📋 测试延续性分析提示词:")
    print("-" * 40)
    
    prompt = llm._build_contextual_prompt(
        instruction="在此基础上，分析各销售员的销售额",
        context="数据包含地区、销售员、销售额等字段，共1000行数据",
        conversation_context=conversation_context,
        metadata={'columns': ['地区', '销售员', '销售额'], 'shape': (1000, 3)},
        table_name="sales_data"
    )
    
    print(prompt)
    print(f"\n📊 提示词长度: {len(prompt)} 字符")
    
    # 检查结构化特征
    sections = [
        "📊 **数据信息**",
        "🏷️ **数据元数据**", 
        "🔄 **对话上下文**",
        "🎯 **当前任务**",
        "💡 **分析指导**",
        "📝 **代码生成要求**"
    ]
    
    found_sections = sum(1 for section in sections if section in prompt)
    print(f"✅ 结构化检查: 找到 {found_sections}/{len(sections)} 个标准部分")
    
    # 检查智能指导原则
    guidance_keywords = ["延续性分析", "变量复用", "分析建议", "技术要求"]
    found_guidance = sum(1 for keyword in guidance_keywords if keyword in prompt)
    print(f"✅ 智能指导检查: 找到 {found_guidance}/{len(guidance_keywords)} 个指导原则")
    
    # 检查是否移除了强制模板
    mandatory_keywords = ["🚨🚨🚨 CRITICAL", "MANDATORY RULE", "必须100%严格执行"]
    found_mandatory = sum(1 for keyword in mandatory_keywords if keyword in prompt)
    print(f"✅ 强制模板移除: {'成功' if found_mandatory == 0 else '失败'} (找到 {found_mandatory} 个强制关键词)")

def test_different_reference_types():
    """测试不同引用类型的智能指导"""
    print("\n🧪 测试不同引用类型的智能指导")
    print("=" * 60)
    
    # 创建LLM实例
    config = TongyiConfig(
        api_key="test_key",
        model="qwen-plus",
        temperature=0.1,
        max_tokens=2000,
        enable_logging=False
    )
    client = TongyiQianwenClient(config)
    llm = EnhancedTongyiLLM(client=client)
    
    # 模拟对话上下文
    conversation_context = {
        'recent_rounds': [
            {
                'user_message': '分析各产品销售额',
                'code': 'product_sales = df.groupby("产品名称")["销售额"].sum().reset_index()',
                'execution_result': {'success': True}
            }
        ]
    }
    
    # 测试不同类型的指令
    test_cases = [
        {
            'instruction': '修改上面的图表，使用饼图展示',
            'expected_type': 'modification'
        },
        {
            'instruction': '对比各产品与各地区的销售表现',
            'expected_type': 'comparison'
        },
        {
            'instruction': '同时分析销售员的表现',
            'expected_type': 'extension'
        },
        {
            'instruction': '分析客户满意度数据',
            'expected_type': 'independent'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['expected_type']}")
        print(f"指令: {test_case['instruction']}")
        
        # 分析上下文
        context_analysis = llm._analyze_conversation_context(
            test_case['instruction'], 
            conversation_context
        )
        
        print(f"检测类型: {context_analysis['reference_type']}")
        print(f"置信度: {context_analysis['confidence']:.2f}")
        
        # 生成指导原则
        guidance = llm._generate_intelligent_guidance(context_analysis, test_case['instruction'])
        print("智能指导:")
        for guide in guidance[:3]:  # 只显示前3条
            print(f"  - {guide}")
        
        # 验证检测准确性
        is_correct = context_analysis['reference_type'] == test_case['expected_type']
        print(f"✅ 检测准确性: {'正确' if is_correct else '错误'}")

def test_guidance_quality():
    """测试指导原则质量"""
    print("\n🧪 测试指导原则质量")
    print("=" * 60)
    
    config = TongyiConfig(
        api_key="test_key",
        model="qwen-plus", 
        temperature=0.1,
        max_tokens=2000,
        enable_logging=False
    )
    client = TongyiQianwenClient(config)
    llm = EnhancedTongyiLLM(client=client)
    
    # 模拟复杂的对话上下文
    conversation_context = {
        'recent_rounds': [
            {
                'user_message': '分析各地区销售额',
                'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                'execution_result': {'success': True}
            },
            {
                'user_message': '找出表现最好的产品',
                'code': 'top_products = df.groupby("产品名称")["销售额"].sum().sort_values(ascending=False)',
                'execution_result': {'success': True}
            }
        ]
    }
    
    instruction = "基于前面的分析，深入研究最佳地区中各产品的销售员表现"
    
    # 分析上下文
    context_analysis = llm._analyze_conversation_context(instruction, conversation_context)
    
    print("📊 上下文分析结果:")
    print(f"  业务背景: {context_analysis['business_context']}")
    print(f"  引用类型: {context_analysis['reference_type']}")
    print(f"  置信度: {context_analysis['confidence']:.2f}")
    print(f"  可用变量: {context_analysis['key_variables']}")
    print(f"  历史操作: {context_analysis['key_operations']}")
    
    # 生成指导原则
    guidance = llm._generate_intelligent_guidance(context_analysis, instruction)
    
    print("\n💡 生成的智能指导原则:")
    for i, guide in enumerate(guidance, 1):
        print(f"  {i}. {guide}")
    
    # 质量评估
    quality_checks = {
        '包含具体建议': any('建议' in guide for guide in guidance),
        '提供技术提示': any('技术' in guide for guide in guidance),
        '避免强制措辞': not any('必须' in guide or '禁止' in guide for guide in guidance),
        '结构清晰': len(guidance) >= 5,
        '内容相关': any('变量' in guide or '分析' in guide for guide in guidance)
    }
    
    print(f"\n✅ 质量评估:")
    for check, passed in quality_checks.items():
        print(f"  {check}: {'通过' if passed else '未通过'} {'✅' if passed else '❌'}")
    
    overall_quality = sum(quality_checks.values()) / len(quality_checks)
    print(f"\n🎯 整体质量评分: {overall_quality:.1%}")

if __name__ == "__main__":
    print("🚀 开始测试结构化提示词和智能指导原则")
    print("=" * 80)
    
    try:
        test_structured_prompt_building()
        test_different_reference_types()
        test_guidance_quality()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成！")
        print("🎯 优化效果:")
        print("  ✅ 实现结构化提示词布局")
        print("  ✅ 移除强制性模板措辞")
        print("  ✅ 生成智能指导原则")
        print("  ✅ 提供灵活的分析建议")
        print("  ✅ 保持专业性和可读性")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
