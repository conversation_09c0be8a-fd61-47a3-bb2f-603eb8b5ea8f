#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控工具 - 监控连续追问功能的性能指标
"""

import time
import functools
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from .logger import get_app_logger

# 可选依赖处理
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    psutil = None


@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    function_name: str
    duration: float
    memory_before: float
    memory_after: float
    timestamp: str
    context_size: int = 0
    token_count: int = 0
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


class PerformanceMonitor:
    """
    性能监控器
    
    监控连续追问功能的关键性能指标：
    1. 响应时间
    2. 内存使用
    3. 上下文处理时间
    4. Token消耗
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化性能监控器
        
        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
        self.metrics: List[PerformanceMetric] = []
        self.monitoring = False
        self.alert_thresholds = {
            'response_time': 15.0,  # 响应时间阈值（秒）
            'memory_usage': 80.0,   # 内存使用阈值（百分比）
            'context_size': 10000,  # 上下文大小阈值（字符）
        }
    
    def monitor_function(self, context_size: int = 0, token_count: int = 0):
        """
        函数性能监控装饰器
        
        Args:
            context_size: 上下文大小
            token_count: Token数量
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 记录开始状态
                start_time = time.time()
                memory_before = self._get_memory_usage()
                
                try:
                    # 执行函数
                    result = func(*args, **kwargs)
                    
                    # 记录结束状态
                    end_time = time.time()
                    memory_after = self._get_memory_usage()
                    duration = end_time - start_time
                    
                    # 创建性能指标
                    metric = PerformanceMetric(
                        function_name=func.__name__,
                        duration=duration,
                        memory_before=memory_before,
                        memory_after=memory_after,
                        timestamp=datetime.now().isoformat(),
                        context_size=context_size,
                        token_count=token_count
                    )
                    
                    # 记录指标
                    self._record_metric(metric)
                    
                    # 检查性能告警
                    self._check_performance_alerts(metric)
                    
                    return result
                    
                except Exception as e:
                    # 记录异常
                    if self.logger:
                        self.logger.error(f"函数 {func.__name__} 执行异常: {str(e)}")
                    raise
            
            return wrapper
        return decorator
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用百分比"""
        if not HAS_PSUTIL:
            return 0.0
        try:
            return psutil.virtual_memory().percent
        except:
            return 0.0
    
    def _record_metric(self, metric: PerformanceMetric):
        """记录性能指标"""
        self.metrics.append(metric)
        
        # 保持最近100条记录
        if len(self.metrics) > 100:
            self.metrics = self.metrics[-100:]
        
        # 记录日志
        if self.logger:
            self.logger.info(
                f"性能指标 - {metric.function_name}: "
                f"耗时 {metric.duration:.2f}s, "
                f"内存 {metric.memory_before:.1f}% -> {metric.memory_after:.1f}%, "
                f"上下文 {metric.context_size} 字符"
            )
    
    def _check_performance_alerts(self, metric: PerformanceMetric):
        """检查性能告警"""
        alerts = []
        
        # 响应时间告警
        if metric.duration > self.alert_thresholds['response_time']:
            alerts.append(f"响应时间过长: {metric.duration:.2f}s")
        
        # 内存使用告警
        if metric.memory_after > self.alert_thresholds['memory_usage']:
            alerts.append(f"内存使用过高: {metric.memory_after:.1f}%")
        
        # 上下文大小告警
        if metric.context_size > self.alert_thresholds['context_size']:
            alerts.append(f"上下文过大: {metric.context_size} 字符")
        
        # 发送告警
        for alert in alerts:
            if self.logger:
                self.logger.warning(f"性能告警 - {metric.function_name}: {alert}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            性能摘要数据
        """
        if not self.metrics:
            return {"message": "暂无性能数据"}
        
        # 计算统计数据
        durations = [m.duration for m in self.metrics]
        memory_usages = [m.memory_after for m in self.metrics]
        context_sizes = [m.context_size for m in self.metrics if m.context_size > 0]
        
        summary = {
            "total_calls": len(self.metrics),
            "avg_duration": sum(durations) / len(durations),
            "max_duration": max(durations),
            "min_duration": min(durations),
            "avg_memory": sum(memory_usages) / len(memory_usages),
            "max_memory": max(memory_usages),
            "current_memory": self._get_memory_usage(),
        }
        
        if context_sizes:
            summary.update({
                "avg_context_size": sum(context_sizes) / len(context_sizes),
                "max_context_size": max(context_sizes),
            })
        
        # 最近的性能趋势
        recent_metrics = self.metrics[-10:]  # 最近10次调用
        if len(recent_metrics) >= 2:
            recent_durations = [m.duration for m in recent_metrics]
            summary["recent_avg_duration"] = sum(recent_durations) / len(recent_durations)
            
            # 性能趋势（是否在恶化）
            first_half = recent_durations[:len(recent_durations)//2]
            second_half = recent_durations[len(recent_durations)//2:]
            
            if first_half and second_half:
                trend = (sum(second_half) / len(second_half)) - (sum(first_half) / len(first_half))
                summary["performance_trend"] = "improving" if trend < 0 else "degrading" if trend > 0.5 else "stable"
        
        return summary
    
    def get_detailed_metrics(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取详细的性能指标
        
        Args:
            limit: 返回的记录数量限制
            
        Returns:
            详细的性能指标列表
        """
        recent_metrics = self.metrics[-limit:] if limit > 0 else self.metrics
        return [asdict(metric) for metric in recent_metrics]
    
    def clear_metrics(self):
        """清空性能指标"""
        self.metrics.clear()
        if self.logger:
            self.logger.info("性能指标已清空")
    
    def export_metrics(self) -> Dict[str, Any]:
        """
        导出性能指标
        
        Returns:
            可序列化的性能数据
        """
        return {
            "export_timestamp": datetime.now().isoformat(),
            "summary": self.get_performance_summary(),
            "detailed_metrics": self.get_detailed_metrics(),
            "alert_thresholds": self.alert_thresholds,
            "system_info": {
                "cpu_count": psutil.cpu_count() if HAS_PSUTIL else "N/A",
                "memory_total": psutil.virtual_memory().total if HAS_PSUTIL else "N/A",
                "current_memory_percent": self._get_memory_usage(),
                "psutil_available": HAS_PSUTIL
            }
        }


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_performance(context_size: int = 0, token_count: int = 0):
    """
    性能监控装饰器的便捷函数
    
    Args:
        context_size: 上下文大小
        token_count: Token数量
    """
    return performance_monitor.monitor_function(context_size, token_count)


def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计信息"""
    return performance_monitor.get_performance_summary()


def clear_performance_data():
    """清空性能数据"""
    performance_monitor.clear_metrics()
