#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用化指导原则
"""

def test_universal_guidance():
    """测试通用化指导原则的适用性"""
    print("🧪 测试通用化指导原则")
    print("=" * 60)
    
    # 模拟不同数据场景的指导生成
    def generate_universal_guidance(ref_type: str) -> list:
        """生成通用化指导原则"""
        guidance = []
        
        if ref_type == 'continuation':
            guidance.extend([
                "🔗 **延续性分析**: 基于之前的分析结果进行深入探索",
                "💡 **数据复用**: 充分利用已有的分析成果，避免重复计算",
                "📋 **分析深化**: 在现有基础上增加新的分析维度",
                "🎯 **交叉分析**: 探索不同维度之间的关联模式"
            ])
        elif ref_type == 'modification':
            guidance.extend([
                "🔧 **修改优化**: 调整或改进之前的分析",
                "💡 **改进方向**: 保持核心逻辑，优化展示方式",
                "🎨 **展示创新**: 尝试不同的可视化方法"
            ])
        elif ref_type == 'comparison':
            guidance.extend([
                "⚖️ **对比分析**: 进行比较分析",
                "💡 **对比思路**: 使用一致的分析方法对比不同维度",
                "📊 **对比展示**: 并排展示便于比较"
            ])
        elif ref_type == 'extension':
            guidance.extend([
                "➕ **扩展分析**: 在现有基础上增加新内容",
                "💡 **扩展思路**: 保持现有分析，增加新的分析角度",
                "🔄 **整合展示**: 将新旧分析有机结合"
            ])
        else:
            guidance.extend([
                "🆕 **独立分析**: 这是一个新的分析任务",
                "💡 **分析建议**: 从数据探索开始，提供全面的分析",
                "📈 **展示建议**: 选择最适合数据特征的可视化方式"
            ])
        
        # 通用技术要求
        guidance.extend([
            "",
            "🛠️ **技术要求**:",
            "  - 确保代码能够独立运行",
            "  - 使用Streamlit组件展示结果",
            "  - 代码简洁高效，注重可读性",
            "  - 添加适当的标题和说明文字"
        ])
        
        return guidance
    
    # 测试不同数据场景的适用性
    test_scenarios = [
        {
            'name': '地区销售分析',
            'context': '第1轮分析了各地区销售额，第2轮要分析销售员',
            'instruction': '在此基础上，分析销售人员销售额',
            'expected_type': 'continuation'
        },
        {
            'name': '产品销售分析', 
            'context': '第1轮分析了各产品销售额，第2轮要分析渠道',
            'instruction': '基于产品分析，进一步分析各渠道表现',
            'expected_type': 'continuation'
        },
        {
            'name': '时间序列分析',
            'context': '第1轮分析了月度趋势，第2轮要分析季度',
            'instruction': '在月度分析基础上，看看季度表现',
            'expected_type': 'continuation'
        },
        {
            'name': '客户分析',
            'context': '第1轮分析了客户分布，第2轮要分析客户价值',
            'instruction': '进一步分析各客户的价值贡献',
            'expected_type': 'continuation'
        },
        {
            'name': '图表修改',
            'context': '第1轮生成了柱状图，第2轮要改成饼图',
            'instruction': '修改上面的图表，改用饼图展示',
            'expected_type': 'modification'
        }
    ]
    
    print("📋 测试不同数据场景的适用性:")
    print("-" * 60)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 场景 {i}: {scenario['name']}")
        print(f"背景: {scenario['context']}")
        print(f"指令: {scenario['instruction']}")
        
        # 生成指导原则
        guidance = generate_universal_guidance(scenario['expected_type'])
        
        print("生成的指导原则:")
        for j, guide in enumerate(guidance[:4], 1):  # 只显示前4条
            print(f"  {j}. {guide}")
        
        # 检查通用性
        has_hardcoded_terms = any(
            term in ' '.join(guidance) 
            for term in ['地区', 'region_sales', '销售员', '产品名称']
        )
        
        is_universal = not has_hardcoded_terms
        provides_direction = any('分析' in guide for guide in guidance)
        
        print(f"✅ 通用性检查:")
        print(f"  无硬编码术语: {'通过' if is_universal else '未通过'} {'✅' if is_universal else '❌'}")
        print(f"  提供分析方向: {'通过' if provides_direction else '未通过'} {'✅' if provides_direction else '❌'}")
    
    print(f"\n📊 总体评估:")
    print("✅ 优势:")
    print("  - 完全通用，适用于任何数据类型")
    print("  - 不包含硬编码的字段名或变量名")
    print("  - 提供方向性指导而非具体实现")
    print("  - 保持LLM的创造性和灵活性")
    
    print("⚠️ 注意事项:")
    print("  - 指导相对抽象，依赖LLM的理解能力")
    print("  - 可能需要LLM自己推断具体的实现方式")
    print("  - 对于复杂场景，可能需要更多上下文信息")

def test_guidance_comparison():
    """对比新旧指导原则"""
    print("\n🔄 新旧指导原则对比")
    print("=" * 60)
    
    print("❌ 旧版本（硬编码）:")
    old_guidance = [
        "💡 **变量复用**: 可以直接使用已存在的变量: region_sales",
        "🔧 **技术提示**: 考虑使用多维度分组，如 `df.groupby(['地区', '销售员'])`",
        "✅ 变量检查: if 'region_sales' not in locals():",
        "✅ 多维分组: df.groupby(['地区', '销售员'])['销售额'].sum()",
        "✅ 基于展示: for region in region_sales['地区']:"
    ]
    
    for guide in old_guidance:
        print(f"  - {guide}")
    
    print(f"\n问题:")
    print("  ❌ 硬编码了 'region_sales', '地区', '销售员'")
    print("  ❌ 假设了特定的数据结构")
    print("  ❌ 过于具体，限制了LLM的创造性")
    print("  ❌ 不适用于其他数据类型")
    
    print(f"\n✅ 新版本（通用化）:")
    new_guidance = [
        "🔗 **延续性分析**: 基于之前的分析结果进行深入探索",
        "💡 **数据复用**: 充分利用已有的分析成果，避免重复计算",
        "📋 **分析深化**: 在现有基础上增加新的分析维度",
        "🎯 **交叉分析**: 探索不同维度之间的关联模式"
    ]
    
    for guide in new_guidance:
        print(f"  - {guide}")
    
    print(f"\n优势:")
    print("  ✅ 完全通用，无硬编码")
    print("  ✅ 适用于任何数据类型和场景")
    print("  ✅ 提供方向而非具体实现")
    print("  ✅ 保持LLM的灵活性和创造性")

if __name__ == "__main__":
    print("🚀 开始测试通用化指导原则")
    print("=" * 80)
    
    try:
        test_universal_guidance()
        test_guidance_comparison()
        
        print("\n" + "=" * 80)
        print("✅ 通用化指导原则测试完成！")
        print("🎯 结论:")
        print("  ✅ 新的指导原则完全通用化")
        print("  ✅ 适用于各种数据类型和分析场景")
        print("  ✅ 避免了硬编码和过度假设")
        print("  ✅ 保持了LLM的创造性和灵活性")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
