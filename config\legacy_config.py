#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit应用配置文件
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class AppConfig:
    """应用配置类"""
    
    # 基本配置
    APP_TITLE = "智能数据分析助手"
    APP_ICON = "🤖"
    PAGE_LAYOUT = "wide"
    
    # 目录配置
    BASE_DIR = Path(__file__).parent
    UPLOAD_DIR = BASE_DIR / "uploaded_files"
    CHAT_HISTORY_DIR = BASE_DIR / "chat_history"
    CHARTS_DIR = BASE_DIR / "charts"
    TEMP_DIR = BASE_DIR / "temp"
    
    # 文件配置
    SUPPORTED_FORMATS = ['.csv', '.xlsx', '.xls', '.txt', '.json']
    MAX_FILE_SIZE_MB = 100
    
    # API配置
    DASHSCOPE_API_KEY = os.getenv('DASHSCOPE_API_KEY')
    DEFAULT_MODEL = "qwen-plus"
    
    # 通义千问模型配置
    AVAILABLE_MODELS = {
        'qwen-turbo': {
            'name': 'qwen-turbo',
            'display_name': '通义千问-Turbo',
            'description': '快速模型，适合简单查询',
            'max_tokens': 1500,
            'temperature': 0.1
        },
        'qwen-plus': {
            'name': 'qwen-plus',
            'display_name': '通义千问-Plus',
            'description': '平衡模型，推荐使用',
            'max_tokens': 2000,
            'temperature': 0.1
        },
        'qwen-max': {
            'name': 'qwen-max',
            'display_name': '通义千问-Max',
            'description': '最强模型，适合复杂分析',
            'max_tokens': 2000,
            'temperature': 0.1
        }
    }
    
    # UI配置
    SIDEBAR_WIDTH = 300
    CHAT_INPUT_PLACEHOLDER = "请输入您的问题..."
    
    # 注意：QUICK_ACTIONS 和 EXAMPLE_QUERIES 配置已被移除
    # 原因：这些编码范例在当前架构中未被使用，已被元数据系统和上下文感知对话替代
    # 备份位置：backup/config_cleanup_20250805_154645/legacy_config.py
    
    # 错误消息
    ERROR_MESSAGES = {
        'no_api_key': "❌ 未找到DASHSCOPE_API_KEY环境变量，请在.env文件中配置",
        'no_data': "请先上传数据文件",
        'file_too_large': f"文件大小超过{MAX_FILE_SIZE_MB}MB限制",
        'unsupported_format': f"不支持的文件格式，支持的格式: {', '.join(SUPPORTED_FORMATS)}",
        'analysis_failed': "❌ 分析失败，请检查您的问题或数据格式",
        'file_load_error': "❌ 文件加载失败",
        'api_error': "❌ API调用失败，请检查网络连接和API密钥"
    }
    
    # 成功消息
    SUCCESS_MESSAGES = {
        'file_uploaded': "✅ 文件上传成功",
        'data_loaded': "✅ 数据加载成功",
        'analysis_complete': "✅ 分析完成！",
        'chat_cleared': "聊天历史已清空",
        'new_session': "新会话已创建"
    }
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.UPLOAD_DIR,
            cls.CHAT_HISTORY_DIR,
            cls.CHARTS_DIR,
            cls.TEMP_DIR
        ]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
    
    @classmethod
    def validate_api_key(cls):
        """验证API密钥"""
        return bool(cls.DASHSCOPE_API_KEY and cls.DASHSCOPE_API_KEY != 'your-dashscope-api-key-here')
    
    @classmethod
    def get_model_config(cls, model_name=None):
        """获取模型配置"""
        model_name = model_name or cls.DEFAULT_MODEL
        return cls.AVAILABLE_MODELS.get(model_name, cls.AVAILABLE_MODELS[cls.DEFAULT_MODEL])

class UIConfig:
    """UI配置类"""
    
    # 颜色主题
    COLORS = {
        'primary': '#1f77b4',
        'success': '#2ca02c',
        'warning': '#ff7f0e',
        'error': '#d62728',
        'info': '#17becf'
    }
    
    # CSS样式
    CUSTOM_CSS = """
    <style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #1f77b4, #17becf);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 10px;
        border-left: 4px solid #1f77b4;
    }
    
    /* 快速操作按钮样式已移除 - 功能不再使用 */
    
    .file-info {
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.2rem 0;
    }
    
    .status-success {
        color: #2ca02c;
        font-weight: bold;
    }
    
    .status-error {
        color: #d62728;
        font-weight: bold;
    }
    
    .status-warning {
        color: #ff7f0e;
        font-weight: bold;
    }
    </style>
    """
    
    # 页面配置
    PAGE_CONFIG = {
        "page_title": AppConfig.APP_TITLE,
        "page_icon": AppConfig.APP_ICON,
        "layout": AppConfig.PAGE_LAYOUT,
        "initial_sidebar_state": "expanded"
    }

# 导出配置实例
app_config = AppConfig()
ui_config = UIConfig()
