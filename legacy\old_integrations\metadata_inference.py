#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能元数据推断模块
基于列名、数据类型和样本数据自动推断元数据配置
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class MetadataInference:
    """智能元数据推断器"""
    
    def __init__(self):
        """初始化推断器"""
        self.business_domain_keywords = {
            "销售管理": ["销售", "营收", "收入", "业绩", "客户", "订单", "交易"],
            "财务分析": ["财务", "成本", "利润", "预算", "资金", "投资", "费用"],
            "库存管理": ["库存", "仓储", "进货", "出货", "盘点", "供应"],
            "人力资源": ["员工", "人员", "薪资", "考勤", "绩效", "招聘"],
            "产品分析": ["产品", "商品", "型号", "规格", "品牌", "类别"],
            "市场营销": ["营销", "推广", "广告", "渠道", "活动", "转化"],
            "客户管理": ["客户", "用户", "会员", "联系", "服务", "满意度"]
        }
        
        self.column_patterns = {
            # 时间相关
            "时间": {
                "keywords": ["日期", "时间", "年", "月", "日", "date", "time", "year", "month", "day"],
                "patterns": [r"\d{4}-\d{2}-\d{2}", r"\d{4}/\d{2}/\d{2}", r"\d{2}:\d{2}:\d{2}"],
                "business_meaning": "时间序列分析和趋势预测的基础维度",
                "tags": ["时间", "维度", "趋势"]
            },
            
            # 金额相关
            "金额": {
                "keywords": ["金额", "价格", "费用", "成本", "收入", "销售额", "营收", "利润", "amount", "price", "cost", "revenue"],
                "data_types": ["float64", "int64"],
                "business_meaning": "财务分析和业绩评估的核心指标",
                "tags": ["财务", "金额", "KPI"],
                "constraints": {"min": 0, "unit": "元"}
            },
            
            # 数量相关
            "数量": {
                "keywords": ["数量", "销量", "库存", "件数", "个数", "quantity", "count", "stock"],
                "data_types": ["int64"],
                "business_meaning": "业务量和规模的重要度量指标",
                "tags": ["数量", "规模", "业务量"],
                "constraints": {"min": 0, "unit": "件"}
            },
            
            # 地理相关
            "地理": {
                "keywords": ["地区", "城市", "省份", "区域", "地址", "location", "city", "region", "address"],
                "business_meaning": "地理分析和区域策略的空间维度",
                "tags": ["地理", "区域", "空间"]
            },
            
            # 人员相关
            "人员": {
                "keywords": ["姓名", "员工", "人员", "销售员", "客服", "经理", "name", "staff", "employee"],
                "business_meaning": "人员管理和绩效分析的主体标识",
                "tags": ["人员", "标识", "管理"]
            },
            
            # 产品相关
            "产品": {
                "keywords": ["产品", "商品", "货品", "物品", "型号", "品牌", "product", "item", "brand", "model"],
                "business_meaning": "产品分析和库存管理的核心标识",
                "tags": ["产品", "标识", "分类"]
            },
            
            # 分类相关
            "分类": {
                "keywords": ["类别", "分类", "类型", "种类", "category", "type", "class"],
                "business_meaning": "数据分组和分类分析的维度标识",
                "tags": ["分类", "维度", "分组"]
            },
            
            # 状态相关
            "状态": {
                "keywords": ["状态", "情况", "结果", "等级", "级别", "status", "state", "level", "grade"],
                "business_meaning": "业务状态和流程管理的标识",
                "tags": ["状态", "流程", "管理"]
            }
        }
    
    def infer_business_domain(self, df: pd.DataFrame) -> str:
        """
        推断业务领域
        
        Args:
            df: DataFrame对象
            
        Returns:
            str: 推断的业务领域
        """
        column_text = " ".join(df.columns.astype(str)).lower()
        
        domain_scores = {}
        for domain, keywords in self.business_domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in column_text)
            if score > 0:
                domain_scores[domain] = score
        
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        else:
            return "通用"
    
    def infer_column_type(self, col_name: str, series: pd.Series) -> str:
        """
        推断列类型
        
        Args:
            col_name: 列名
            series: 列数据
            
        Returns:
            str: 推断的列类型
        """
        col_name_lower = col_name.lower()
        data_type = str(series.dtype)
        
        # 获取非空样本值
        sample_values = series.dropna().astype(str).head(10).tolist()
        sample_text = " ".join(sample_values).lower()
        
        # 计算每种类型的匹配分数
        type_scores = {}
        
        for col_type, config in self.column_patterns.items():
            score = 0
            
            # 关键词匹配
            keywords = config.get("keywords", [])
            for keyword in keywords:
                if keyword in col_name_lower:
                    score += 3
                if keyword in sample_text:
                    score += 1
            
            # 数据类型匹配
            if "data_types" in config and data_type in config["data_types"]:
                score += 2
            
            # 模式匹配
            patterns = config.get("patterns", [])
            for pattern in patterns:
                if any(re.search(pattern, str(val)) for val in sample_values):
                    score += 2
            
            if score > 0:
                type_scores[col_type] = score
        
        # 返回得分最高的类型
        if type_scores:
            return max(type_scores, key=type_scores.get)
        else:
            return "通用"
    
    def analyze_column_statistics(self, series: pd.Series) -> Dict[str, Any]:
        """
        分析列统计信息
        
        Args:
            series: 列数据
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            "non_null_count": series.count(),
            "null_count": series.isnull().sum(),
            "unique_count": series.nunique(),
            "data_type": str(series.dtype)
        }
        
        # 数值类型统计
        if pd.api.types.is_numeric_dtype(series):
            stats.update({
                "min": float(series.min()) if not pd.isna(series.min()) else None,
                "max": float(series.max()) if not pd.isna(series.max()) else None,
                "mean": float(series.mean()) if not pd.isna(series.mean()) else None,
                "std": float(series.std()) if not pd.isna(series.std()) else None
            })
        
        # 文本类型统计
        elif pd.api.types.is_string_dtype(series) or pd.api.types.is_object_dtype(series):
            non_null_series = series.dropna()
            if len(non_null_series) > 0:
                str_lengths = non_null_series.astype(str).str.len()
                stats.update({
                    "avg_length": float(str_lengths.mean()),
                    "max_length": int(str_lengths.max()),
                    "min_length": int(str_lengths.min())
                })
        
        return stats
    
    def generate_column_description(self, col_name: str, col_type: str, stats: Dict[str, Any]) -> str:
        """
        生成列描述
        
        Args:
            col_name: 列名
            col_type: 列类型
            stats: 统计信息
            
        Returns:
            str: 生成的描述
        """
        base_descriptions = {
            "时间": f"{col_name}字段，记录事件发生的时间信息",
            "金额": f"{col_name}字段，表示相关的金额数值",
            "数量": f"{col_name}字段，表示相关的数量或计数",
            "地理": f"{col_name}字段，表示地理位置或区域信息",
            "人员": f"{col_name}字段，表示相关人员的标识信息",
            "产品": f"{col_name}字段，表示产品或商品的标识信息",
            "分类": f"{col_name}字段，用于数据分类和分组",
            "状态": f"{col_name}字段，表示相关的状态或等级信息",
            "通用": f"{col_name}字段的数据信息"
        }
        
        description = base_descriptions.get(col_type, base_descriptions["通用"])
        
        # 添加统计信息
        if stats.get("unique_count"):
            if stats["unique_count"] == stats["non_null_count"]:
                description += "，每个值都是唯一的"
            elif stats["unique_count"] < 10:
                description += f"，包含{stats['unique_count']}种不同的值"
        
        return description
    
    def infer_table_metadata(self, table_name: str, df: pd.DataFrame) -> Dict[str, Any]:
        """
        推断完整的表格元数据
        
        Args:
            table_name: 表格名称
            df: DataFrame对象
            
        Returns:
            Dict: 完整的元数据配置
        """
        logger.info(f"开始推断表格 {table_name} 的元数据")
        
        # 推断业务领域
        business_domain = self.infer_business_domain(df)
        
        # 生成表格描述
        table_description = f"{table_name}数据表，包含{len(df.columns)}个字段和{len(df)}条记录"
        if business_domain != "通用":
            table_description += f"，属于{business_domain}领域"
        
        # 推断列元数据
        columns_metadata = {}
        potential_primary_keys = []
        
        for col_name in df.columns:
            series = df[col_name]
            
            # 推断列类型
            col_type = self.infer_column_type(col_name, series)
            
            # 分析统计信息
            stats = self.analyze_column_statistics(series)
            
            # 生成描述
            description = self.generate_column_description(col_name, col_type, stats)
            
            # 获取配置模板
            config_template = self.column_patterns.get(col_type, {})
            
            # 生成示例值
            examples = []
            non_null_values = series.dropna().unique()
            if len(non_null_values) > 0:
                # 安全的示例值生成（过滤无穷大值）
                for val in non_null_values[:3]:
                    try:
                        # 检查是否是数值类型且为无穷大
                        if pd.api.types.is_numeric_dtype(type(val)) and np.isinf(val):
                            continue  # 跳过无穷大值
                        if pd.isna(val):
                            continue  # 跳过NaN值
                        examples.append(str(val))
                    except (TypeError, ValueError):
                        # 对于非数值类型，直接添加字符串表示
                        if not pd.isna(val):
                            examples.append(str(val))
            
            # 检查是否可能是主键
            if stats["unique_count"] == stats["non_null_count"] and stats["non_null_count"] > 0:
                potential_primary_keys.append(col_name)
            
            # 构建列元数据
            column_metadata = {
                "name": col_name,
                "display_name": col_name,
                "description": description,
                "data_type": stats["data_type"],
                "business_meaning": config_template.get("business_meaning", "需要进一步定义业务含义"),
                "examples": examples,
                "constraints": config_template.get("constraints", {}),
                "tags": config_template.get("tags", ["未分类"]),
                "inferred_type": col_type,
                "statistics": stats
            }
            
            columns_metadata[col_name] = column_metadata
        
        # 构建完整元数据
        metadata = {
            "table_name": table_name,
            "description": table_description,
            "business_domain": business_domain,
            "columns": columns_metadata,
            "relationships": {},
            "primary_keys": potential_primary_keys[:2],  # 最多选择2个主键
            "inference_info": {
                "inferred_at": datetime.now().isoformat(),
                "total_columns": len(df.columns),
                "total_rows": len(df),
                "confidence_score": self._calculate_confidence_score(columns_metadata)
            }
        }
        
        logger.info(f"表格 {table_name} 元数据推断完成，置信度: {metadata['inference_info']['confidence_score']:.2f}")
        
        return metadata
    
    def _calculate_confidence_score(self, columns_metadata: Dict[str, Any]) -> float:
        """
        计算推断置信度分数
        
        Args:
            columns_metadata: 列元数据
            
        Returns:
            float: 置信度分数 (0-1)
        """
        total_score = 0
        max_score = 0
        
        for col_metadata in columns_metadata.values():
            max_score += 1
            
            # 如果不是通用类型，增加置信度
            if col_metadata.get("inferred_type") != "通用":
                total_score += 0.8
            else:
                total_score += 0.3
            
            # 如果有示例值，增加置信度
            if col_metadata.get("examples"):
                total_score += 0.2
        
        return total_score / max_score if max_score > 0 else 0.0

# 创建全局实例
metadata_inference = MetadataInference()
