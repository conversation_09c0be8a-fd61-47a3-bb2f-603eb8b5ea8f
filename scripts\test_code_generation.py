#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码生成和执行
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.processors.metadata_processor import MetadataProcessor
from core.processors.code_cleaner import CodeCleaner
import pandas as pd

def test_prompt_generation():
    """测试提示词生成"""
    print("🧪 测试提示词生成...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'customer_id': [1001, 1002, 1003],
        'product_name': ['iPhone 14', 'MacBook Pro', 'iPad Air'],
        'sales_amount': [999.99, 2499.00, 599.00],
        'sale_date': pd.to_datetime(['2024-01-15', '2024-02-20', '2024-03-10'])
    })
    
    # 创建处理器
    processor = MetadataProcessor()
    
    # 准备上下文（安全版本 - 无敏感数据）
    context = f"""数据形状: {test_data.shape}
列名: {list(test_data.columns)}
数据类型:
{test_data.dtypes.to_string()}
数据质量摘要:
- 总记录数: {len(test_data)}
- 缺失值情况: {'存在缺失值' if test_data.isnull().any().any() else '无缺失值'}
- 重复记录: {'存在重复' if test_data.duplicated().any() else '无重复'}"""
    
    # 测试基础提示词
    instruction = "分析销售数据，找出销售金额最高的产品"
    basic_prompt = processor._build_basic_prompt(instruction, context)
    
    print("✅ 基础提示词生成成功")
    print("关键检查点:")
    print(f"  - 包含'df'变量说明: {'数据已经加载在变量 df 中' in basic_prompt}")
    print(f"  - 包含Streamlit要求: {'st.write' in basic_prompt}")
    print(f"  - 不要求读取文件: {'不要重新读取文件' in basic_prompt}")
    
    return basic_prompt

def test_code_cleaning():
    """测试代码清理"""
    print("\n🧪 测试代码清理...")
    
    # 模拟LLM生成的有问题的代码
    problematic_code = """
```python
import pandas as pd

# 读取数据
data = pd.read_csv('sales_data.csv')

# 分析销售金额最高的产品
result = data.groupby('product_name')['sales_amount'].sum().sort_values(ascending=False)
print("销售金额最高的产品:")
print(result.head())
```
"""
    
    # 创建代码清理器
    cleaner = CodeCleaner()
    
    # 清理代码
    cleaned_code = cleaner.clean(problematic_code)
    
    print("✅ 代码清理完成")
    print("修复检查:")
    print(f"  - 移除markdown标记: {'```' not in cleaned_code}")
    print(f"  - 替换data为df: {'data.groupby' not in cleaned_code and 'df.groupby' in cleaned_code}")
    print(f"  - 移除文件读取: {'read_csv' not in cleaned_code}")
    print(f"  - 保留pandas导入: {'import pandas as pd' in cleaned_code}")
    
    print("\n清理后的代码:")
    print("-" * 40)
    print(cleaned_code)
    print("-" * 40)
    
    return cleaned_code

def test_code_execution():
    """测试代码执行"""
    print("\n🧪 测试代码执行...")
    
    # 创建测试数据
    df = pd.DataFrame({
        'customer_id': [1001, 1002, 1003, 1004, 1005],
        'product_name': ['iPhone 14', 'MacBook Pro', 'iPad Air', 'iPhone 14', 'MacBook Pro'],
        'sales_amount': [999.99, 2499.00, 599.00, 999.99, 2499.00],
        'sale_date': pd.to_datetime(['2024-01-15', '2024-02-20', '2024-03-10', '2024-03-15', '2024-03-20'])
    })
    
    # 测试代码
    test_code = """
import pandas as pd

# 分析销售金额最高的产品
result = df.groupby('product_name')['sales_amount'].sum().sort_values(ascending=False)
print("销售金额最高的产品:")
print(result.head())

# 显示详细信息
print(f"\\n最高销售金额产品: {result.index[0]}")
print(f"总销售金额: {result.iloc[0]:.2f}")
"""
    
    try:
        # 执行代码
        exec_globals = {'df': df, 'pd': pd}
        exec(test_code, exec_globals)
        
        print("✅ 代码执行成功")
        return True
        
    except Exception as e:
        print(f"❌ 代码执行失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始代码生成和执行测试")
    print("=" * 50)
    
    try:
        # 测试提示词生成
        prompt = test_prompt_generation()
        
        # 测试代码清理
        cleaned_code = test_code_cleaning()
        
        # 测试代码执行
        execution_success = test_code_execution()
        
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        print(f"   提示词生成: ✅ 通过")
        print(f"   代码清理: ✅ 通过")
        print(f"   代码执行: {'✅ 通过' if execution_success else '❌ 失败'}")
        
        if execution_success:
            print("\n🎉 所有测试通过！代码生成和执行问题已修复！")
            print("\n💡 修复要点:")
            print("1. 提示词明确告知LLM数据在df变量中")
            print("2. 代码清理器自动修复常见问题")
            print("3. 移除文件读取操作，直接使用内存数据")
            return 0
        else:
            print("\n⚠️ 代码执行测试失败")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
