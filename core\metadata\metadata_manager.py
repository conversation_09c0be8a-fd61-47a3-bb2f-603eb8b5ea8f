#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理模块 - 适配新架构
为AI数据分析平台提供表格列名解释和元数据管理功能
帮助大语言模型更好地理解数据结构和业务含义
"""

import json
import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from dataclasses import dataclass, asdict
import re

# 导入新架构的日志
from ..utils.logger import get_app_logger

@dataclass
class ColumnMetadata:
    """列元数据类"""
    name: str                    # 列名
    display_name: str           # 显示名称
    description: str            # 详细描述
    data_type: str             # 数据类型
    business_meaning: str      # 业务含义
    examples: List[str]        # 示例值
    constraints: Dict[str, Any] # 约束条件
    tags: List[str]            # 标签
    created_at: str            # 创建时间
    updated_at: str            # 更新时间

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()

@dataclass
class TableMetadata:
    """表格元数据类"""
    table_name: str                      # 表格名称
    description: str                     # 表格描述
    business_domain: str                 # 业务领域
    columns: Dict[str, ColumnMetadata]   # 列元数据
    relationships: Dict[str, str]        # 列间关系
    primary_keys: List[str]             # 主键列
    created_at: str                     # 创建时间
    updated_at: str                     # 更新时间
    version: str                        # 版本号

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
        if not self.version:
            self.version = "1.0.0"

class MetadataManager:
    """元数据管理器 - 适配新架构"""
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化元数据管理器

        Args:
            config_dir: 配置文件目录，如果为None则使用全局配置
        """
        # 设置配置目录
        if config_dir is None:
            # 默认使用项目根目录下的metadata_config
            project_root = Path(__file__).parent.parent.parent
            self.config_dir = project_root / "metadata_config"
        else:
            self.config_dir = config_dir
        self.config_dir = Path(self.config_dir)
        self.config_dir.mkdir(exist_ok=True)

        # 使用新架构的日志系统
        self.logger = get_app_logger()

        # 创建完整的目录结构
        self._ensure_directory_structure()

        # 配置文件路径
        self.tables_config_file = self.config_dir / "tables_metadata.json"
        self.templates_config_file = self.config_dir / "column_templates.json"
        self.backups_dir = self.config_dir / "backups"
        self.logs_dir = self.config_dir / "logs"
        self.exports_dir = self.config_dir / "exports"

        # 加载配置
        self.tables_metadata: Dict[str, TableMetadata] = {}
        self.column_templates: Dict[str, Dict] = {}

        self._load_configurations()
        self._initialize_default_templates()

    def _ensure_directory_structure(self):
        """确保完整的目录结构存在"""
        subdirs = [
            "backups",      # 备份目录
            "logs",         # 日志目录
            "exports",      # 导出目录
            "templates",    # 模板目录
            "imports"       # 导入目录
        ]
        
        for subdir in subdirs:
            (self.config_dir / subdir).mkdir(exist_ok=True)
        
        self.logger.info(f"元数据目录结构已创建: {self.config_dir}")

    def _load_configurations(self):
        """加载配置文件"""
        try:
            # 加载表格元数据
            if self.tables_config_file.exists():
                with open(self.tables_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for table_name, table_data in data.items():
                        # 重构列元数据
                        columns = {}
                        for col_name, col_data in table_data.get('columns', {}).items():
                            columns[col_name] = ColumnMetadata(**col_data)
                        
                        table_data['columns'] = columns
                        self.tables_metadata[table_name] = TableMetadata(**table_data)
                
                self.logger.info(f"已加载 {len(self.tables_metadata)} 个表格的元数据")

            # 加载列模板
            if self.templates_config_file.exists():
                with open(self.templates_config_file, 'r', encoding='utf-8') as f:
                    self.column_templates = json.load(f)
                
                self.logger.info(f"已加载 {len(self.column_templates)} 个列模板")

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")

    def _initialize_default_templates(self):
        """初始化默认模板"""
        if not self.column_templates:
            self.column_templates = {
                "id": {
                    "display_name": "ID",
                    "description": "唯一标识符",
                    "data_type": "int64",
                    "business_meaning": "主键，用于唯一标识记录",
                    "constraints": {"unique": True, "not_null": True},
                    "tags": ["主键", "标识符"]
                },
                "name": {
                    "display_name": "名称",
                    "description": "名称字段",
                    "data_type": "object",
                    "business_meaning": "实体的名称或标题",
                    "constraints": {"not_null": True},
                    "tags": ["名称", "文本"]
                },
                "date": {
                    "display_name": "日期",
                    "description": "日期时间字段",
                    "data_type": "datetime64[ns]",
                    "business_meaning": "记录的时间信息",
                    "constraints": {},
                    "tags": ["时间", "日期"]
                },
                "amount": {
                    "display_name": "金额",
                    "description": "金额数值",
                    "data_type": "float64",
                    "business_meaning": "货币金额或数量",
                    "constraints": {"min": 0},
                    "tags": ["金额", "数值"]
                }
            }
            self._save_templates()

    def create_table_metadata(self, table_name: str, description: str = "",
                            business_domain: str = "") -> TableMetadata:
        """创建新的表格元数据"""
        current_time = datetime.now().isoformat()
        table_metadata = TableMetadata(
            table_name=table_name,
            description=description,
            business_domain=business_domain,
            columns={},
            relationships={},
            primary_keys=[],
            created_at=current_time,
            updated_at=current_time,
            version="1.0.0"
        )
        
        self.tables_metadata[table_name] = table_metadata
        self._save_tables_metadata()
        
        self.logger.info(f"已创建表格元数据: {table_name}")
        return table_metadata

    def add_column_metadata(self, table_name: str, column_name: str, 
                          display_name: str = "", description: str = "",
                          data_type: str = "", business_meaning: str = "",
                          examples: List[str] = None, 
                          constraints: Dict[str, Any] = None,
                          tags: List[str] = None) -> ColumnMetadata:
        """添加列元数据"""
        if table_name not in self.tables_metadata:
            self.create_table_metadata(table_name)

        current_time = datetime.now().isoformat()
        column_metadata = ColumnMetadata(
            name=column_name,
            display_name=display_name or column_name,
            description=description,
            data_type=data_type,
            business_meaning=business_meaning,
            examples=examples or [],
            constraints=constraints or {},
            tags=tags or [],
            created_at=current_time,
            updated_at=current_time
        )

        self.tables_metadata[table_name].columns[column_name] = column_metadata
        self.tables_metadata[table_name].updated_at = datetime.now().isoformat()
        
        self._save_tables_metadata()
        
        self.logger.info(f"已添加列元数据: {table_name}.{column_name}")
        return column_metadata

    def get_table_metadata(self, table_name: str) -> Optional[TableMetadata]:
        """获取表格元数据"""
        return self.tables_metadata.get(table_name)

    def get_column_metadata(self, table_name: str, column_name: str) -> Optional[ColumnMetadata]:
        """获取列元数据"""
        table_metadata = self.get_table_metadata(table_name)
        if table_metadata:
            return table_metadata.columns.get(column_name)
        return None

    def get_all_tables(self) -> Dict[str, TableMetadata]:
        """获取所有表格元数据"""
        return self.tables_metadata

    def delete_table_metadata(self, table_name: str) -> bool:
        """删除表格元数据"""
        if table_name in self.tables_metadata:
            del self.tables_metadata[table_name]
            self._save_tables_metadata()
            self.logger.info(f"已删除表格元数据: {table_name}")
            return True
        return False

    def delete_column_metadata(self, table_name: str, column_name: str) -> bool:
        """删除列元数据"""
        table_metadata = self.get_table_metadata(table_name)
        if table_metadata and column_name in table_metadata.columns:
            del table_metadata.columns[column_name]
            table_metadata.updated_at = datetime.now().isoformat()
            self._save_tables_metadata()
            self.logger.info(f"已删除列元数据: {table_name}.{column_name}")
            return True
        return False

    def _save_tables_metadata(self):
        """保存表格元数据到文件"""
        try:
            # 创建备份
            self._create_backup()
            
            # 转换为可序列化的格式
            data = {}
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                # 转换列元数据
                columns_dict = {}
                for col_name, col_metadata in table_dict['columns'].items():
                    columns_dict[col_name] = col_metadata
                table_dict['columns'] = columns_dict
                data[table_name] = table_dict

            with open(self.tables_config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info("表格元数据已保存")

        except Exception as e:
            self.logger.error(f"保存表格元数据失败: {e}")

    def _save_templates(self):
        """保存模板到文件"""
        try:
            with open(self.templates_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.column_templates, f, ensure_ascii=False, indent=2)
            
            self.logger.info("列模板已保存")

        except Exception as e:
            self.logger.error(f"保存列模板失败: {e}")

    def _create_backup(self):
        """创建备份文件"""
        if self.tables_config_file.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backups_dir / f"tables_metadata_backup_{timestamp}.json"
            
            import shutil
            shutil.copy2(self.tables_config_file, backup_file)
            
            # 只保留最近10个备份
            backups = sorted(self.backups_dir.glob("tables_metadata_backup_*.json"))
            if len(backups) > 10:
                for old_backup in backups[:-10]:
                    old_backup.unlink()

    def export_metadata(self, table_name: str, format: str = "json") -> Optional[str]:
        """导出元数据"""
        table_metadata = self.get_table_metadata(table_name)
        if not table_metadata:
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format.lower() == "json":
            export_file = self.exports_dir / f"{table_name}_metadata_{timestamp}.json"
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(table_metadata), f, ensure_ascii=False, indent=2)
        
        elif format.lower() == "yaml":
            export_file = self.exports_dir / f"{table_name}_metadata_{timestamp}.yaml"
            with open(export_file, 'w', encoding='utf-8') as f:
                yaml.dump(asdict(table_metadata), f, allow_unicode=True, default_flow_style=False)
        
        else:
            return None

        self.logger.info(f"元数据已导出: {export_file}")
        return str(export_file)

    def generate_llm_context(self, table_name: str) -> str:
        """为LLM生成上下文信息"""
        table_metadata = self.get_table_metadata(table_name)
        if not table_metadata:
            return ""

        context_parts = [
            f"表格名称: {table_metadata.table_name}",
            f"表格描述: {table_metadata.description}",
            f"业务领域: {table_metadata.business_domain}",
            "",
            "列信息:"
        ]

        for col_name, col_metadata in table_metadata.columns.items():
            col_info = [
                f"  - {col_name} ({col_metadata.display_name})",
                f"    描述: {col_metadata.description}",
                f"    数据类型: {col_metadata.data_type}",
                f"    业务含义: {col_metadata.business_meaning}"
            ]
            
            if col_metadata.examples:
                col_info.append(f"    示例值: {', '.join(col_metadata.examples[:3])}")
            
            if col_metadata.tags:
                col_info.append(f"    标签: {', '.join(col_metadata.tags)}")
            
            context_parts.extend(col_info)
            context_parts.append("")

        if table_metadata.relationships:
            context_parts.append("列间关系:")
            for rel_desc in table_metadata.relationships.values():
                context_parts.append(f"  - {rel_desc}")

        return "\n".join(context_parts)


# 全局元数据管理器实例
metadata_manager = MetadataManager()
